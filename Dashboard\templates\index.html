{% extends 'base.html' %}

{% block title %}HexMES Wireless Admin - Home{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <img src="{{ url_for('static', filename='images/HexMES_dark.png') }}" alt="HexMES Wireless Admin"
                class="hexmes-logo-lg me-2">
        </h1>
        <p class="lead">Central management for Wireless Device instances</p>
    </div>
    <div class="col-auto d-flex align-items-center">
        <a href="{{ url_for('add_bridge_route') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i>Add Device
        </a>
    </div>
</div>

{%- set has_any_devices =
(bridges|length > 0) or
(serial_devices|length > 0) or
(cts_devices|length > 0) or
(custom_devices|length > 0) or
(w610_devices|length > 0) -%}
<div class="row">
    {% if bridges %}
    {% for bridge in bridges %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100 bridge-card" data-bridge-id="{{ bridge.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-pc-display me-2"></i>{{ bridge.name }}
                </h5>
                <span
                    class="badge status-badge {% if bridge.status == 'online' %}bg-success{% else %}bg-danger{% endif %}">
                    {{ bridge.status }}
                </span>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <p class="mb-1"><strong>IP Address:</strong> {{ bridge.ip }}</p>
                    <p class="mb-1"><strong>Port:</strong> {{ bridge.port }}</p>
                    <p class="mb-0">
                        <strong>Last Seen:</strong>
                        {% if bridge.last_seen %}
                        <span class="last-seen-time">{{ bridge.last_seen }}</span>
                        {% else %}
                        Never
                        {% endif %}
                    </p>
                </div>

                <div class="mt-3">
                    <h6 class="border-bottom pb-2">Connected Devices</h6>
                    {% set bridge_devices = [] %}
                    {% for device in devices %}
                    {% if device.bridge_id == bridge.id %}
                    {% set _ = bridge_devices.append(device) %}
                    {% endif %}
                    {% endfor %}

                    {% if bridge_devices %}
                    <div class="list-group">
                        {% for device in bridge_devices %}
                        <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                            data-device-id="{{ device.id }}" data-hioki-id="{{ device.hioki_id }}">
                            <div>
                                <strong>{{ device.hioki_id }}</strong>
                                <div class="small text-muted">Port: {{ device.port }}</div>
                                <div class="small" data-reading-cell>{{ device.last_reading or 'N/A' }}</div>
                            </div>
                            <button class="btn btn-sm btn-outline-danger remove-device-btn"
                                data-bridge-id="{{ bridge.id }}" data-device-id="{{ device.id }}"
                                data-port="{{ device.port }}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No devices connected</p>
                    {% endif %}
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('bridge_detail', bridge_id=bridge.id) }}" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-gear me-1"></i>Manage
                </a>
                <button class="btn btn-sm btn-outline-primary check-status-btn ms-1" data-bridge-id="{{ bridge.id }}">
                    <i class="bi bi-arrow-repeat me-1"></i>Check Status
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endif %}
    {% if not has_any_devices %}
    <div class="col-12">
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>No devices have been added yet.
            <a href="{{ url_for('add_bridge_route') }}" class="alert-link">Add your first device</a>
        </div>
    </div>
    {% endif %}
</div>

{% if serial_devices %}
<h3 class="mt-4 mb-3">Serial Devices</h3>
<div class="row">
    {% for device in serial_devices %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-serial-id="{{ device.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-usb me-2"></i>{{ device.hioki_id }}</h5>
                <span
                    class="badge status-badge {% if device.status != 'offline' %}bg-success{% else %}bg-danger{% endif %}">{{
                    device.status != 'offline' and 'online' or 'offline' }}</span>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>IP Address:</strong> {{ device.ip }}</p>
                <p class="mb-1"><strong>Port:</strong> {{ device.port }}</p>
                <p class="mb-1"><strong>Type:</strong> {{ device.tester_type }}</p>
                <p class="mb-1"><strong>Last Reading:</strong> {{ device.last_reading or 'N/A' }}</p>
                <p class="mb-0"><strong>Last Seen:</strong> {% if device.last_update %}<span class="last-seen-time">{{
                        device.last_update }}</span>{% else %}Never{% endif %}</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-primary check-serial-status-btn"
                    data-device-id="{{ device.id }}"><i class="bi bi-arrow-repeat me-1"></i>Check Status</button>
                <button class="btn btn-sm btn-outline-danger remove-serial-btn ms-1" data-device-id="{{ device.id }}"><i
                        class="bi bi-trash"></i></button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

{% if w610_devices %}
<h3 class="mt-4 mb-3">
    Hioki over W610
    <button class="btn btn-sm btn-outline-info ms-2" id="showW610ServerStatus">
        <i class="bi bi-info-circle"></i> Server Status
    </button>
</h3>
<div class="row">
    {% for device in w610_devices %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-w610-id="{{ device.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-wifi me-2"></i>{{ device.hioki_id }}</h5>
                <div>
                    <span
                        class="badge status-badge {% if device.status != 'offline' %}bg-success{% else %}bg-danger{% endif %}">
                        {{ 'online' if device.status != 'offline' else 'offline' }}
                    </span>
                    <span class="badge bg-info ms-1 connection-badge" data-hioki-id="{{ device.hioki_id }}">
                        Checking...
                    </span>
                </div>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>Type:</strong> {{ device.tester_type }}</p>
                <p class="mb-1"><strong>IP Address:</strong> {{ device.ip }}</p>
                <p class="mb-1"><strong>Last Reading:</strong>
                    <span class="device-reading" data-hioki-id="{{ device.hioki_id }}">
                        {{ device.last_reading or 'N/A' }}
                    </span>
                </p>
                <p class="mb-1"><strong>Last Seen:</strong>
                    <span class="last-seen-time">
                        {% if device.last_update %}{{ device.last_update }}{% else %}Never{% endif %}
                    </span>
                </p>
                <p class="mb-0 connection-details" data-hioki-id="{{ device.hioki_id }}">
                    <small class="text-muted">Connection details loading...</small>
                </p>
            </div>
            <div class="card-footer d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary manage-w610-btn" data-device-id="{{ device.id }}"
                    data-hioki-id="{{ device.hioki_id }}">
                    <i class="bi bi-gear me-1"></i>Manage
                </button>
                <button class="btn btn-sm btn-outline-primary check-w610-status-btn" data-device-id="{{ device.id }}">
                    <i class="bi bi-arrow-repeat me-1"></i>Status
                </button>
                <button class="btn btn-sm btn-outline-danger remove-w610-btn" data-device-id="{{ device.id }}">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}



{% if cts_devices %}
<h3 class="mt-4 mb-3">CTS Testers</h3>
<div class="row">
    {% for cts in cts_devices %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-cts-id="{{ cts.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-box me-2"></i>{{ cts.name }}</h5>
                <span
                    class="badge status-badge {% if cts.status == 'online' %}bg-success{% else %}bg-danger{% endif %}">{{
                    cts.status }}</span>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>IP Address:</strong> {{ cts.ip }}</p>
                <p class="mb-1"><strong>Port:</strong> {{ cts.port }}</p>
                <p class="mb-1"><strong>CTS ID:</strong> {{ cts.cts_id }}</p>
                <p class="mb-1"><strong>Type:</strong> {{ cts.cts_type }}</p>
                <p class="mb-1"><strong>Last Reading:</strong> <span class="cts-reading">{{ cts.last_reading or 'N/A'
                        }}</span></p>
                <p class="mb-0"><strong>Last Seen:</strong> {% if cts.last_update %}<span class="last-seen-time">{{
                        cts.last_update }}</span>{% else %}Never{% endif %}</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-primary check-cts-status-btn" data-cts-id="{{ cts.id }}"><i
                        class="bi bi-arrow-repeat me-1"></i>Check Status</button>
                <button class="btn btn-sm btn-outline-danger remove-cts-btn ms-1" data-cts-id="{{ cts.id }}"><i
                        class="bi bi-trash"></i></button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

{% if welders %}
<h3 class="mt-4 mb-3">Ultrasonic Welders</h3>
<div class="row">
    {% for welder in welders %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-welder-id="{{ welder.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-hammer me-2"></i>{{ welder.welder_name or welder.raspi_name }}
                </h5>
                <span
                    class="badge status-badge {% if welder.status == 'online' %}bg-success{% else %}bg-danger{% endif %}">
                    {{ welder.status }}
                </span>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-6"><strong>Raspi:</strong></div>
                    <div class="col-6 text-end">{{ welder.raspi_name }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6"><strong>IP Address:</strong></div>
                    <div class="col-6 text-end">{{ welder.ip }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6"><strong>Port:</strong></div>
                    <div class="col-6 text-end">{{ welder.port }}</div>
                </div>

                <!-- Enhanced Welder Data Display -->
                <div class="mb-2">
                    <strong>Last Weld Data:</strong>
                    <div class="welder-data-container mt-1">
                        {% if welder.last_reading %}
                        {% set welder_data = welder.last_reading | safe %}
                        {% if welder_data and welder_data != 'N/A' %}
                        <div class="welder-data-grid">
                            <div id="welder-data-{{ welder.id }}" class="welder-parsed-data"
                                data-welder-id="{{ welder.id }}"
                                data-welder-data="{{ welder_data | tojson | safe | e }}"
                                data-fallback-text="{{ welder.last_reading | truncate(100) | e }}">
                                <!-- Data will be populated by JavaScript -->
                            </div>
                        </div>
                        <script>
                            document.addEventListener('DOMContentLoaded', function () {
                                const container = document.getElementById('welder-data-{{ welder.id }}');
                                try {
                                    const rawData = JSON.parse(container.dataset.welderData);
                                    const welderId = parseInt(container.dataset.welderId);
                                    displayWelderData(welderId, rawData);
                                } catch (e) {
                                    // Fallback for non-JSON data
                                    container.innerHTML = '<div class="text-muted"><small>' +
                                        container.dataset.fallbackText + '</small></div>';
                                }
                            });
                        </script>
                        {% else %}
                        <div class="text-muted">
                            <small>No weld data available</small>
                        </div>
                        {% endif %}
                        {% else %}
                        <div class="text-muted">
                            <small>No weld data available</small>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-6"><strong>Last Seen:</strong></div>
                    <div class="col-6 text-end">
                        {% if welder.last_update %}
                        <span class="last-seen-time">{{ welder.last_update }}</span>
                        {% else %}
                        Never
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex gap-1 flex-wrap">
                <button class="btn btn-sm btn-outline-primary check-welder-status-btn" data-welder-id="{{ welder.id }}">
                    <i class="bi bi-arrow-repeat me-1"></i>Check Status
                </button>
                <button class="btn btn-sm btn-outline-secondary retry-welder-mqtt-btn" data-welder-id="{{ welder.id }}">
                    <i class="bi bi-cloud-arrow-up me-1"></i>Retry MQTT
                </button>
                <button class="btn btn-sm btn-outline-info view-raw-data-btn" data-welder-id="{{ welder.id }}"
                    title="View Raw Data">
                    <i class="bi bi-file-text me-1"></i>Raw
                </button>
                <button class="btn btn-sm btn-outline-danger remove-welder-btn" data-welder-id="{{ welder.id }}">
                    <i class="bi bi-trash"></i>Remove
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

<!-- Raw Data Modal -->
<div class="modal fade" id="welderRawDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Raw Welder Data</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label"><strong>Welder:</strong></label>
                    <div id="rawDataWelderName" class="form-control-plaintext"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>Raw JSON Data:</strong></label>
                    <textarea id="rawDataContent" class="form-control" rows="15" readonly></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="copyRawData()">
                    <i class="bi bi-clipboard"></i> Copy to Clipboard
                </button>
            </div>
        </div>
    </div>
</div>
<style>
    .welder-data-container {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    [data-bs-theme="dark"] .welder-data-container {
        background-color: #2d2d2d;
        border-color: #444;
    }

    .welder-data-grid {
        font-size: 0.85rem;
    }

    .welder-data-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.25rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    [data-bs-theme="dark"] .welder-data-item {
        border-bottom-color: #444;
    }

    .welder-data-item:last-child {
        border-bottom: none;
    }

    .welder-data-key {
        font-weight: 500;
        color: #6c757d;
        flex: 0 0 40%;
    }

    .welder-data-value {
        flex: 1;
        text-align: right;
        font-family: 'Courier New', monospace;
    }

    .welder-nested-object {
        margin-top: 0.25rem;
        padding-left: 1rem;
        border-left: 2px solid #28a745;
    }

    .welder-array-item {
        margin: 0.1rem 0;
        padding: 0.1rem 0.5rem;
        background-color: rgba(40, 167, 69, 0.1);
        border-radius: 0.25rem;
        font-size: 0.8rem;
    }

    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 0.25rem;
    }

    .status-success {
        background-color: #28a745;
    }

    .status-warning {
        background-color: #ffc107;
    }

    .status-danger {
        background-color: #dc3545;
    }

    .status-info {
        background-color: #17a2b8;
    }
</style>

<script>
    // Function to display parsed welder data
    function displayWelderData(welderId, data) {
        const container = document.getElementById(`welder-data-${welderId}`);
        if (!container) return;

        if (!data || typeof data !== 'object') {
            container.innerHTML = '<div class="text-muted"><small>Invalid data format</small></div>';
            return;
        }

        let html = '';

        // Priority fields to show first
        const priorityFields = [
            'status', 'state', 'weld_result', 'energy', 'power', 'amplitude',
            'time', 'frequency', 'force', 'displacement', 'temperature'
        ];

        // Display priority fields first
        priorityFields.forEach(key => {
            if (data.hasOwnProperty(key)) {
                html += formatDataItem(key, data[key]);
            }
        });

        // Display remaining fields
        Object.keys(data).forEach(key => {
            if (!priorityFields.includes(key)) {
                html += formatDataItem(key, data[key]);
            }
        });

        container.innerHTML = html || '<div class="text-muted"><small>No data to display</small></div>';
    }

    // Function to format individual data items
    function formatDataItem(key, value, isNested = false) {
        const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        if (value === null || value === undefined) {
            return `<div class="welder-data-item ${isNested ? 'ms-2' : ''}">
            <span class="welder-data-key">${formattedKey}:</span>
            <span class="welder-data-value text-muted">null</span>
        </div>`;
        }

        if (typeof value === 'boolean') {
            const statusClass = value ? 'status-success' : 'status-danger';
            return `<div class="welder-data-item ${isNested ? 'ms-2' : ''}">
            <span class="welder-data-key">${formattedKey}:</span>
            <span class="welder-data-value">
                <span class="status-indicator ${statusClass}"></span>${value ? 'Yes' : 'No'}
            </span>
        </div>`;
        }

        if (typeof value === 'number') {
            const formattedValue = formatNumericValue(key, value);
            return `<div class="welder-data-item ${isNested ? 'ms-2' : ''}">
            <span class="welder-data-key">${formattedKey}:</span>
            <span class="welder-data-value">${formattedValue}</span>
        </div>`;
        }

        if (typeof value === 'string') {
            const colorClass = getStatusColor(key, value);
            return `<div class="welder-data-item ${isNested ? 'ms-2' : ''}">
            <span class="welder-data-key">${formattedKey}:</span>
            <span class="welder-data-value ${colorClass}">${value}</span>
        </div>`;
        }

        if (Array.isArray(value)) {
            let html = `<div class="welder-data-item ${isNested ? 'ms-2' : ''}">
            <span class="welder-data-key">${formattedKey}:</span>
            <span class="welder-data-value">[${value.length} items]</span>
        </div>`;

            if (value.length > 0 && value.length <= 5) {
                html += '<div class="welder-nested-object">';
                value.forEach((item, index) => {
                    if (typeof item === 'object') {
                        html += `<div class="welder-array-item"><strong>[${index}]:</strong> ${JSON.stringify(item, null, 1)}</div>`;
                    } else {
                        html += `<div class="welder-array-item"><strong>[${index}]:</strong> ${item}</div>`;
                    }
                });
                html += '</div>';
            }
            return html;
        }

        if (typeof value === 'object') {
            let html = `<div class="welder-data-item ${isNested ? 'ms-2' : ''}">
            <span class="welder-data-key">${formattedKey}:</span>
            <span class="welder-data-value">{${Object.keys(value).length} props}</span>
        </div>`;

            if (Object.keys(value).length <= 3) {
                html += '<div class="welder-nested-object">';
                Object.keys(value).forEach(subKey => {
                    html += formatDataItem(subKey, value[subKey], true);
                });
                html += '</div>';
            }
            return html;
        }

        return `<div class="welder-data-item ${isNested ? 'ms-2' : ''}">
        <span class="welder-data-key">${formattedKey}:</span>
        <span class="welder-data-value">${String(value)}</span>
    </div>`;
    }

    // Function to format numeric values with appropriate units
    function formatNumericValue(key, value) {
        const lowerKey = key.toLowerCase();

        if (lowerKey.includes('energy')) return `${value.toFixed(2)} J`;
        if (lowerKey.includes('power')) return `${value.toFixed(1)} W`;
        if (lowerKey.includes('force')) return `${value.toFixed(1)} N`;
        if (lowerKey.includes('frequency')) return `${value.toFixed(0)} Hz`;
        if (lowerKey.includes('amplitude')) return `${value.toFixed(1)} %`;
        if (lowerKey.includes('time') || lowerKey.includes('duration')) return `${value.toFixed(3)} s`;
        if (lowerKey.includes('temp')) return `${value.toFixed(1)} °C`;
        if (lowerKey.includes('displacement') || lowerKey.includes('distance')) return `${value.toFixed(3)} mm`;
        if (lowerKey.includes('pressure')) return `${value.toFixed(1)} bar`;
        if (lowerKey.includes('voltage')) return `${value.toFixed(1)} V`;
        if (lowerKey.includes('current')) return `${value.toFixed(2)} A`;

        // For very small numbers, use scientific notation
        if (Math.abs(value) < 0.001 && value !== 0) {
            return value.toExponential(3);
        }

        // For large numbers, add thousand separators
        if (Math.abs(value) >= 1000) {
            return value.toLocaleString();
        }

        // Default formatting
        if (value % 1 === 0) return value.toString();
        return value.toFixed(3);
    }

    // Function to get appropriate color class for status values
    function getStatusColor(key, value) {
        const lowerKey = key.toLowerCase();
        const lowerValue = String(value).toLowerCase();

        if (lowerKey.includes('status') || lowerKey.includes('state') || lowerKey.includes('result')) {
            if (lowerValue.includes('ok') || lowerValue.includes('pass') ||
                lowerValue.includes('success') || lowerValue.includes('good')) {
                return 'text-success';
            }
            if (lowerValue.includes('fail') || lowerValue.includes('error') ||
                lowerValue.includes('bad') || lowerValue.includes('alarm')) {
                return 'text-danger';
            }
            if (lowerValue.includes('warn') || lowerValue.includes('caution')) {
                return 'text-warning';
            }
        }

        return '';
    }

    // Raw data modal functionality
    document.addEventListener('DOMContentLoaded', function () {
        // Handle raw data button clicks
        document.querySelectorAll('.view-raw-data-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const welderId = this.getAttribute('data-welder-id');
                showRawData(welderId);
            });
        });
    });

    function showRawData(welderId) {
        // Find the welder data
        fetch(`/api/ultrasonic/${welderId}/raw_data`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('rawDataWelderName').textContent = data.welder_name || `Welder ${welderId}`;
                document.getElementById('rawDataContent').value = JSON.stringify(data.raw_data, null, 2);

                const modal = new bootstrap.Modal(document.getElementById('welderRawDataModal'));
                modal.show();
            })
            .catch(error => {
                console.error('Error fetching raw data:', error);
                alert('Error fetching raw data');
            });
    }

    function copyRawData() {
        const textarea = document.getElementById('rawDataContent');
        textarea.select();
        document.execCommand('copy');

        // Show feedback
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check"></i> Copied!';
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-success');

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-primary');
        }, 2000);
    }
</script>

{% if custom_devices %}
<h3 class="mt-4 mb-3">Custom Devices</h3>
<div class="row">
    {% for device in custom_devices %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100" data-custom-id="{{ device.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-gear me-2"></i>{{ device.name }}</h5>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>IP Address:</strong> {{ device.ip }}</p>
                <p class="mb-1"><strong>Port:</strong> {{ device.port }}</p>
                <p class="mb-1"><strong>Incoming:</strong> {{ device.in_protocol }}</p>
                <p class="mb-0"><strong>Outgoing:</strong> {{ device.out_protocol }}</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-danger remove-custom-btn" data-custom-id="{{ device.id }}"><i
                        class="bi bi-trash"></i></button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

<!-- Add Device Modal -->
<div class="modal fade" id="removeDeviceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Device</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove this device?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRemoveDevice">Remove</button>
            </div>
        </div>
    </div>
</div>
<!-- W610 Server Status Modal -->
<div class="modal fade" id="w610ServerStatusModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">W610 Server Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="w610ServerStatusContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="refreshServerStatus">
                    <i class="bi bi-arrow-repeat"></i> Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced W610 Management Modal -->
<div class="modal fade" id="manageW610Modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Manage W610 Device: <span id="currentDeviceId"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Quick Commands</h6>
                        <div class="d-grid gap-2 mb-3">
                            <button type="button" class="btn btn-primary w610-command-btn" data-command="FETCh?">
                                <i class="bi bi-download"></i> Trigger Reading (FETCh?)
                            </button>
                            <button type="button" class="btn btn-info w610-command-btn" data-command="*IDN?">
                                <i class="bi bi-info-circle"></i> Get Device Info (*IDN?)
                            </button>
                            <button type="button" class="btn btn-warning w610-command-btn" data-command="*RST">
                                <i class="bi bi-arrow-clockwise"></i> Reset Device (*RST)
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Custom Command</h6>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="customCommand"
                                placeholder="Enter SCPI command..." value="">
                            <button class="btn btn-outline-primary" type="button" id="sendCustomCommand">
                                Send
                            </button>
                        </div>
                        <small class="text-muted">
                            Examples: FETCh?, *IDN?, :SYST:ERR?, :MEAS:IMP?
                        </small>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-12">
                        <h6>Connection Status</h6>
                        <div id="connectionStatusDetails">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-12">
                        <h6>Command Response Log</h6>
                        <div id="commandResponseLog" class="border rounded p-2"
                            style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                            <small class="text-muted">Command responses will appear here...</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-outline-danger" id="clearResponseLog">
                    Clear Log
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize variables for device removal
    let removeContext = null;

    // Setup event listeners when the DOM is loaded
    document.addEventListener('DOMContentLoaded', function () {
        // Check status buttons for bridges
        document.querySelectorAll('.check-status-btn').forEach(button => {
            button.addEventListener('click', function () {
                const bridgeId = this.getAttribute('data-bridge-id');
                const icon = this.querySelector('i');
                icon?.classList.add('spin');
                checkBridgeStatus(bridgeId).finally(() => icon?.classList.remove('spin'));
            });
        });

        // Serial device events
        document.querySelectorAll('.check-serial-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-device-id');
                const icon = this.querySelector('i');
                icon?.classList.add('spin');
                checkSerialStatus(id).finally(() => icon?.classList.remove('spin'));
            });
        });
        document.querySelectorAll('.remove-serial-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'serial', id: this.getAttribute('data-device-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // W610 device events
        document.querySelectorAll('.check-w610-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-device-id');
                const icon = this.querySelector('i');
                icon?.classList.add('spin');
                checkW610Status(id).finally(() => icon?.classList.remove('spin'));
            });
        });
        document.querySelectorAll('.remove-w610-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'w610', id: this.getAttribute('data-device-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // HTTP device events
        document.querySelectorAll('.check-http-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-device-id');
                const icon = this.querySelector('i');
                icon?.classList.add('spin');
                checkHttpStatus(id).finally(() => icon?.classList.remove('spin'));
            });
        });
        document.querySelectorAll('.remove-http-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'http', id: this.getAttribute('data-device-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // CTS device events
        document.querySelectorAll('.check-cts-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-cts-id');
                const icon = this.querySelector('i');
                icon?.classList.add('spin');
                checkCtsStatus(id).finally(() => icon?.classList.remove('spin'));
            });
        });
        document.querySelectorAll('.remove-cts-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'cts', id: this.getAttribute('data-cts-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // Ultrasonic welder events
        document.querySelectorAll('.check-welder-status-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-welder-id');
                const icon = this.querySelector('i');
                icon?.classList.add('spin');
                checkWelderStatus(id).finally(() => icon?.classList.remove('spin'));
            });
        });
        document.querySelectorAll('.retry-welder-mqtt-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-welder-id');
                retryWelderMqtt(id);
            });
        });
        document.querySelectorAll('.remove-welder-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'welder', id: this.getAttribute('data-welder-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // Custom device events
        document.querySelectorAll('.remove-custom-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                removeContext = { type: 'custom', id: this.getAttribute('data-custom-id') };
                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // Remove bridge connected device buttons
        document.querySelectorAll('.remove-device-btn').forEach(button => {
            button.addEventListener('click', function () {
                removeContext = {
                    type: 'bridge',
                    bridgeId: this.getAttribute('data-bridge-id'),
                    deviceId: this.getAttribute('data-device-id'),
                    port: this.getAttribute('data-port')
                };

                const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
                modal.show();
            });
        });

        // Confirm remove device button
        document.getElementById('confirmRemoveDevice').addEventListener('click', function () {
            if (!removeContext) return;
            if (removeContext.type === 'bridge') {
                removeDevice(removeContext.bridgeId, removeContext.deviceId, removeContext.port);
            } else if (removeContext.type === 'serial') {
                removeSerialDevice(removeContext.id);
            } else if (removeContext.type === 'w610') {
                removeW610Device(removeContext.id);
            } else if (removeContext.type === 'http') {
                removeHttpDevice(removeContext.id);
            } else if (removeContext.type === 'cts') {
                removeCtsDevice(removeContext.id);
            } else if (removeContext.type === 'welder') {
                removeWelder(removeContext.id);
            } else if (removeContext.type === 'custom') {
                removeCustomDevice(removeContext.id);
            }
        });
    });

    // Function to check bridge status
    function checkBridgeStatus(bridgeId) {
        const statusBadge = document.querySelector(`.bridge-card[data-bridge-id="${bridgeId}"] .status-badge`);

        // Show checking status
        if (statusBadge) {
            statusBadge.textContent = "checking...";
            statusBadge.classList.remove('bg-success', 'bg-danger');
            statusBadge.classList.add('bg-secondary');
        }

        // Make API request
        return fetch(`/api/bridges/${bridgeId}/check_status`)
            .then(response => response.json())
            .then(data => {
                if (statusBadge) {
                    statusBadge.textContent = data.status;

                    if (data.status === 'online') {
                        statusBadge.classList.remove('bg-danger', 'bg-secondary');
                        statusBadge.classList.add('bg-success');
                    } else {
                        statusBadge.classList.remove('bg-success', 'bg-secondary');
                        statusBadge.classList.add('bg-danger');
                    }
                }
            })
            .catch(error => {
                console.error('Error checking status:', error);
                if (statusBadge) {
                    statusBadge.textContent = "error";
                    statusBadge.classList.remove('bg-success', 'bg-secondary');
                    statusBadge.classList.add('bg-danger');
                }
            });
    }

    // Function to remove a device
    function removeDevice(bridgeId, deviceId, port) {
        fetch(`/api/bridges/${bridgeId}/devices/${deviceId}/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ port: port })
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Close the modal
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();

                    // Reload the page to reflect changes
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error removing device:', error);
                alert('Error removing device. Please try again.');
            });
    }

    function removeSerialDevice(deviceId) {
        fetch(`/api/serial/${deviceId}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing serial device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeW610Device(deviceId) {
        fetch(`/api/w610/${deviceId}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing W610 device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeCtsDevice(ctsId) {
        fetch(`/api/cts/${ctsId}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing CTS device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeCustomDevice(id) {
        fetch(`/api/custom/${id}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing custom device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeHttpDevice(id) {
        fetch(`/api/http/${id}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing HTTP device:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function removeWelder(id) {
        fetch(`/api/ultrasonic/${id}/remove`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status === 'success') {
                    bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
                    window.location.reload();
                } else {
                    alert('Error removing device: ' + data.message);
                }
            })
            .catch(err => {
                console.error('Error removing welder:', err);
                alert('Error removing device. Please try again.');
            });
    }

    function checkSerialStatus(id) {
        return fetch(`/api/serial/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-serial-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status === 'online');
                    badge.classList.toggle('bg-danger', data.status !== 'online');
                }
            })
            .catch(err => console.error('Error checking serial status:', err));
    }

    function checkHttpStatus(id) {
        return fetch(`/api/http/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-http-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status === 'online');
                    badge.classList.toggle('bg-danger', data.status !== 'online');
                }
            })
            .catch(err => console.error('Error checking http status:', err));
    }

    function checkW610Status(id) {
        return fetch(`/api/w610/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-w610-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status !== 'offline');
                    badge.classList.toggle('bg-danger', data.status === 'offline');
                }
            })
            .catch(err => console.error('Error checking W610 status:', err));
    }

    function checkCtsStatus(id) {
        return fetch(`/api/cts/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-cts-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status === 'online');
                    badge.classList.toggle('bg-danger', data.status !== 'online');
                }
            })
            .catch(err => console.error('Error checking CTS status:', err));
    }

    function checkWelderStatus(id) {
        return fetch(`/api/ultrasonic/${id}/check_status`)
            .then(resp => resp.json())
            .then(data => {
                const badge = document.querySelector(`[data-welder-id="${id}"] .status-badge`);
                if (badge) {
                    badge.textContent = data.status;
                    badge.classList.toggle('bg-success', data.status === 'online');
                    badge.classList.toggle('bg-danger', data.status !== 'online');
                }
            })
            .catch(err => console.error('Error checking welder status:', err));
    }

    function retryWelderMqtt(id) {
        fetch(`/api/ultrasonic/${id}/retry_mqtt`, { method: 'POST' })
            .then(resp => resp.json())
            .then(data => {
                if (data.status !== 'success') {
                    alert('Error retrying MQTT: ' + (data.message || 'unknown'));
                }
            })
            .catch(err => {
                console.error('Error retrying MQTT:', err);
            });
    }
</script>
<script>
    // Add this JavaScript to handle W610 device management
    document.addEventListener('DOMContentLoaded', function () {
        let currentHiokiId = null;

        // Load connection status for all W610 devices
        function loadW610ConnectionStatus() {
            fetch('/api/w610/connected_devices')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Update connection badges
                        document.querySelectorAll('.connection-badge').forEach(badge => {
                            const hiokiId = badge.getAttribute('data-hioki-id');
                            const deviceInfo = data.devices[hiokiId];

                            if (deviceInfo) {
                                badge.textContent = 'Connected';
                                badge.classList.remove('bg-warning', 'bg-danger');
                                badge.classList.add('bg-success');

                                // Update connection details
                                const detailsElement = document.querySelector(`.connection-details[data-hioki-id="${hiokiId}"]`);
                                if (detailsElement) {
                                    const connectedAt = new Date(deviceInfo.connected_at).toLocaleString();
                                    const readingCount = deviceInfo.readings_received || 0;
                                    detailsElement.innerHTML = `
                                    <small class="text-success">
                                        Connected since: ${connectedAt}<br>
                                        Readings received: ${readingCount}
                                    </small>
                                `;
                                }
                            } else {
                                badge.textContent = 'Disconnected';
                                badge.classList.remove('bg-success', 'bg-warning');
                                badge.classList.add('bg-danger');

                                const detailsElement = document.querySelector(`.connection-details[data-hioki-id="${hiokiId}"]`);
                                if (detailsElement) {
                                    detailsElement.innerHTML = `<small class="text-danger">Not connected to server</small>`;
                                }
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading W610 connection status:', error);
                });
        }

        // Show server status modal
        document.getElementById('showW610ServerStatus')?.addEventListener('click', function () {
            const modal = new bootstrap.Modal(document.getElementById('w610ServerStatusModal'));
            modal.show();
            loadServerStatus();
        });

        // Refresh server status
        document.getElementById('refreshServerStatus')?.addEventListener('click', loadServerStatus);

        function loadServerStatus() {
            const content = document.getElementById('w610ServerStatusContent');
            content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

            fetch('/api/w610/connected_devices')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const connectedCount = Object.keys(data.devices).length;
                        let html = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Server Status</h6>
                                <p><strong>Status:</strong> <span class="badge bg-${data.server_status === 'running' ? 'success' : 'danger'}">${data.server_status}</span></p>
                                <p><strong>Listening Port:</strong> {{ w610_server_port }}</p>
                                <p><strong>Connected Devices:</strong> ${connectedCount}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Configuration</h6>
                                <p><strong>Server Host:</strong> {{ w610_server_host }}</p>
                                <p><strong>W610 devices should connect to:</strong><br>
                                   <code>${window.location.hostname}:{{ w610_server_port }}</code></p>
                            </div>
                        </div>
                    `;

                        if (connectedCount > 0) {
                            html += '<h6 class="mt-3">Connected Devices</h6><div class="table-responsive"><table class="table table-sm"><thead><tr><th>Hioki ID</th><th>IP Address</th><th>Connected Since</th><th>Readings</th></tr></thead><tbody>';

                            for (const [hiokiId, info] of Object.entries(data.devices)) {
                                html += `
                                <tr>
                                    <td>${hiokiId}</td>
                                    <td>${info.ip_address}</td>
                                    <td>${new Date(info.connected_at).toLocaleString()}</td>
                                    <td>${info.readings_received || 0}</td>
                                </tr>
                            `;
                            }

                            html += '</tbody></table></div>';
                        }

                        content.innerHTML = html;
                    } else {
                        content.innerHTML = '<div class="alert alert-danger">Error loading server status</div>';
                    }
                })
                .catch(error => {
                    content.innerHTML = '<div class="alert alert-danger">Error connecting to server</div>';
                    console.error('Error loading server status:', error);
                });
        }

        // Enhanced manage W610 modal
        document.querySelectorAll('.manage-w610-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                currentHiokiId = this.getAttribute('data-hioki-id');
                document.getElementById('currentDeviceId').textContent = currentHiokiId;

                const modal = new bootstrap.Modal(document.getElementById('manageW610Modal'));
                modal.show();

                // Load connection details
                loadConnectionDetails(currentHiokiId);

                // Clear previous log
                document.getElementById('commandResponseLog').innerHTML = '<small class="text-muted">Command responses will appear here...</small>';
            });
        });

        // Quick command buttons
        document.querySelectorAll('.w610-command-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const command = this.getAttribute('data-command');
                sendCommand(currentHiokiId, command);
            });
        });

        // Custom command
        document.getElementById('sendCustomCommand')?.addEventListener('click', function () {
            const command = document.getElementById('customCommand').value.trim();
            if (command) {
                sendCommand(currentHiokiId, command);
            }
        });

        // Clear response log
        document.getElementById('clearResponseLog')?.addEventListener('click', function () {
            document.getElementById('commandResponseLog').innerHTML = '<small class="text-muted">Command responses will appear here...</small>';
        });

        function loadConnectionDetails(hiokiId) {
            const container = document.getElementById('connectionStatusDetails');
            container.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div></div>';

            fetch(`/api/w610/${hiokiId}/connection_status`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'connected') {
                        const info = data.device_info;
                        container.innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Status:</strong> <span class="badge bg-success">Connected</span></p>
                                <p><strong>IP Address:</strong> ${info.ip_address}</p>
                                <p><strong>Connected Since:</strong> ${new Date(info.connected_at).toLocaleString()}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Readings Received:</strong> ${info.readings_received || 0}</p>
                                <p><strong>Last Reading:</strong> ${info.last_reading_time ? new Date(info.last_reading_time).toLocaleString() : 'Never'}</p>
                            </div>
                        </div>
                    `;
                    } else {
                        container.innerHTML = `
                        <div class="alert alert-warning">
                            <strong>Disconnected:</strong> ${data.message}
                        </div>
                    `;
                    }
                })
                .catch(error => {
                    container.innerHTML = '<div class="alert alert-danger">Error loading connection status</div>';
                    console.error('Error loading connection details:', error);
                });
        }

        function sendCommand(hiokiId, command) {
            const logContainer = document.getElementById('commandResponseLog');
            const timestamp = new Date().toLocaleTimeString();

            // Add command to log
            const cmdDiv = document.createElement('div');
            cmdDiv.innerHTML = `<small class="text-primary">[${timestamp}] > ${command}</small>`;
            logContainer.appendChild(cmdDiv);

            fetch(`/api/w610/${hiokiId}/send_command`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ command: command })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        if (data.response !== undefined) {
                            const respDiv = document.createElement('div');
                            const displayResp = data.response || 'No response';
                            respDiv.innerHTML = `<small class="text-success">[${timestamp}] &lt; ${displayResp}</small>`;
                            logContainer.appendChild(respDiv);
                        }
                        if (data.reading) {
                            const readingDiv = document.createElement('div');
                            readingDiv.innerHTML = `<small class="text-muted">[${timestamp}] Reading: ${data.reading}</small>`;
                            logContainer.appendChild(readingDiv);
                        }
                    } else {
                        const errorDiv = document.createElement('div');
                        errorDiv.innerHTML = `<small class="text-danger">[${timestamp}] Error: ${data.message}</small>`;
                        logContainer.appendChild(errorDiv);
                    }
                    logContainer.scrollTop = logContainer.scrollHeight;
                })
                .catch(error => {
                    const errorDiv = document.createElement('div');
                    errorDiv.innerHTML = `<small class="text-danger">[${timestamp}] Network error: ${error.message}</small>`;
                    logContainer.appendChild(errorDiv);
                    logContainer.scrollTop = logContainer.scrollHeight;
                });
        }

        // Load connection status on page load and refresh periodically
        loadW610ConnectionStatus();
        setInterval(loadW610ConnectionStatus, 10000); // Every 10 seconds
    });
</script>
{% endblock %}