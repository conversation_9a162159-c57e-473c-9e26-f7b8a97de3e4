import socket
import threading
import time
import logging
import queue
from datetime import datetime
from typing import Dict, Optional

from .data_formatter import format_data, create_mqtt_topic
from ..services import db_service
from ..services import mqtt_service


class W610Server(threading.Thread):
    """Server that listens for incoming W610 device connections."""

    DEFAULT_PORT = 15000

    # Initialization commands sent to each device upon connection
    AUTOINIT = [
        "*CLS",
        ":SYST:BEEP:STAT OFF",  # optional: silence panel beeps while testing
        ":SYST:DATA OUT ON",
        ":TRIG:SOUR INT",
        ":INIT:CONT ON",
    ]

    def __init__(
        self, stop_event: threading.Event, host: str = "0.0.0.0", port: int = None
    ):
        super().__init__(daemon=True)
        self.stop_event = stop_event
        self.host = host
        self.port = port or self.DEFAULT_PORT
        self.logger = logging.getLogger(__name__)
        self.server_socket: Optional[socket.socket] = None
        self.client_handlers: Dict[str, "W610<PERSON><PERSON>Handler"] = {}  # IP -> handler
        self.device_connections: Dict[str, "W610ClientHandler"] = (
            {}
        )  # hioki_id -> handler
        self._lock = threading.RLock()  # For thread-safe access to connection dicts

    def run(self):
        """Main server loop - listen for incoming connections."""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(10)

            self.logger.info(f"W610 Server listening on {self.host}:{self.port}")

            while not self.stop_event.is_set():
                try:
                    # Set timeout so we can check stop_event periodically
                    self.server_socket.settimeout(1.0)
                    conn, addr = self.server_socket.accept()

                    # Handle new connection in separate thread
                    client_ip = addr[0]
                    self.logger.info(f"New W610 connection from {client_ip}:{addr[1]}")

                    # Find device by IP
                    device = self._find_device_by_ip(client_ip)
                    if device:
                        handler = W610ClientHandler(
                            conn, addr, device, self.stop_event, self
                        )
                        handler.start()

                        with self._lock:
                            self.client_handlers[client_ip] = handler
                            self.device_connections[device["hioki_id"]] = handler

                        self.logger.info(
                            f"Started handler for device {device['hioki_id']} at {client_ip}"
                        )
                    else:
                        self.logger.warning(
                            f"No device configured for IP {client_ip}, closing connection"
                        )
                        conn.close()

                except socket.timeout:
                    # Normal timeout, continue loop to check stop_event
                    continue
                except Exception as e:
                    if not self.stop_event.is_set():
                        self.logger.error(f"Error accepting connection: {e}")

        except Exception as e:
            self.logger.error(f"Server error: {e}")
        finally:
            self._cleanup()

    def send_command_to_device(self, hioki_id: str, command: str) -> Optional[str]:
        """Send a command to a specific device by Hioki ID and return the response.

        Args:
            hioki_id: The Hioki device ID (e.g., 'H001')
            command: The SCPI command to send (e.g., 'FETCh?', '*IDN?')

        Returns:
            The raw response string from the device. ``None`` indicates the
            command could not be sent (device offline or error). An empty string
            means the command was sent but no response was received.
        """
        with self._lock:
            handler = self.device_connections.get(hioki_id)
            if handler and handler.is_connected():
                try:
                    return handler.send_command(command)
                except Exception as e:
                    self.logger.error(f"Error sending command to {hioki_id}: {e}")
                    return None
            else:
                self.logger.warning(f"Device {hioki_id} not connected or not found")
                return None

    def get_connected_devices(self) -> Dict[str, Dict]:
        """Get list of currently connected devices with their info.

        Returns:
            dict: Mapping of hioki_id to device info including connection status
        """
        connected_devices = {}
        with self._lock:
            for hioki_id, handler in self.device_connections.items():
                if handler and handler.is_connected():
                    connected_devices[hioki_id] = {
                        "hioki_id": hioki_id,
                        "ip_address": handler.addr[0],
                        "port": handler.addr[1],
                        "connected_at": handler.connected_at,
                        "last_reading_time": getattr(
                            handler, "last_reading_time", None
                        ),
                        "readings_received": getattr(handler, "readings_received", 0),
                    }
        return connected_devices

    def device_disconnected(self, handler: "W610ClientHandler"):
        """Called when a device handler disconnects - cleanup references.

        Args:
            handler: The disconnected handler instance
        """
        with self._lock:
            # Remove from both tracking dictionaries
            client_ip = handler.addr[0]
            hioki_id = handler.device["hioki_id"]

            if client_ip in self.client_handlers:
                del self.client_handlers[client_ip]

            if hioki_id in self.device_connections:
                del self.device_connections[hioki_id]

        self.logger.info(f"Cleaned up disconnected device {hioki_id} from {client_ip}")

    def _find_device_by_ip(self, ip: str) -> Optional[Dict]:
        """Find W610 device configuration by IP address."""
        try:
            devices = db_service.get_w610_devices()
            for device in devices:
                if device.get("ip") == ip:
                    return device
            return None
        except Exception as e:
            self.logger.error(f"Error finding device by IP {ip}: {e}")
            return None

    def stop(self):
        """Stop the server and all client handlers."""
        self.logger.info("Stopping W610 Server")
        self.stop_event.set()

        # Stop all client handlers
        for handler in self.client_handlers.values():
            handler.stop()

        # Close server socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except Exception:
                pass

    def _cleanup(self):
        """Clean up resources."""
        with self._lock:
            for handler in self.client_handlers.values():
                handler.stop()
            self.client_handlers.clear()
            self.device_connections.clear()

        if self.server_socket:
            try:
                self.server_socket.close()
            except Exception:
                pass

        self.logger.info("W610 Server stopped")


class W610ClientHandler(threading.Thread):
    """Handles a single W610 device connection."""

    def __init__(
        self,
        conn: socket.socket,
        addr: tuple,
        device: Dict,
        stop_event: threading.Event,
        server: W610Server,
    ):
        super().__init__(daemon=True)
        self.conn = conn
        self.addr = addr
        self.device = device
        self.stop_event = stop_event
        self._local_stop = threading.Event()
        self.server = server
        self.logger = logging.getLogger(__name__)

        # Connection tracking
        self.connected_at = datetime.now()
        self.last_reading_time = None
        self.readings_received = 0
        self._is_connected = True
        self._send_lock = threading.Lock()  # For thread-safe command sending
        # Queue to capture responses from commands
        self.response_queue: "queue.Queue[str]" = queue.Queue()

    def is_connected(self) -> bool:
        """Check if this handler is still connected."""
        return self._is_connected and not self._local_stop.is_set()

    def send_command(
        self, command: str, *, timeout: float = 2.0
    ) -> Optional[str]:
        """Send a command to the connected device and wait for a response.

        Args:
            command: SCPI command to send (CRLF will be added automatically)
            timeout: Seconds to wait for a response before giving up

        Returns:
            The raw response string. ``None`` if the command could not be sent or
            an error occurred. An empty string indicates the command was sent but
            no response was received within ``timeout``.
        """

        if not self.is_connected():
            return None

        hioki_id = self.device["hioki_id"]

        try:
            with self._send_lock:
                # Clear any stale responses
                while not self.response_queue.empty():
                    try:
                        self.response_queue.get_nowait()
                    except queue.Empty:
                        break

                # Add CRLF if not present
                if not command.endswith(("\r\n", "\n")):
                    command += "\r\n"

                self.conn.sendall(command.encode("ascii", "ignore"))
                self.logger.info(
                    f"Sent command to {hioki_id}: {command.strip()}"
                )

            # Wait for a response from the reader thread
            try:
                response = self.response_queue.get(timeout=timeout)
                return response
            except queue.Empty:
                self.logger.warning(
                    f"No response from {hioki_id} for command {command.strip()}"
                )
                return ""

        except Exception as e:
            self.logger.error(
                f"Error sending command to {hioki_id}: {e}"
            )
            return None

    def run(self):
        """Handle the device connection."""
        client_ip = self.addr[0]
        hioki_id = self.device["hioki_id"]

        try:
            self.logger.info(f"Handling W610 device {hioki_id} from {client_ip}")

            # Set up keepalive
            self._setup_keepalive()

            # Send initialization commands
            self._send_init_commands()

            # Mark device as online
            db_service.update_device_status_by_hioki_id(hioki_id, "online")

            # Listen for readings
            self._listen_for_readings()

        except Exception as e:
            self.logger.error(f"Error handling W610 device {hioki_id}: {e}")
        finally:
            # Mark as disconnected
            self._is_connected = False

            # Notify server of disconnection
            self.server.device_disconnected(self)

            # Mark device as offline
            db_service.update_device_status_by_hioki_id(hioki_id, "offline")

            # Close connection
            try:
                self.conn.close()
            except Exception:
                pass

            self.logger.info(f"W610 device {hioki_id} disconnected from {client_ip}")

    def _setup_keepalive(self):
        """Set up TCP keepalive options."""
        try:
            self.conn.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            # Linux keepalive options
            for opt, val in [
                ("TCP_KEEPIDLE", 60),
                ("TCP_KEEPINTVL", 10),
                ("TCP_KEEPCNT", 3),
            ]:
                if hasattr(socket, opt):
                    self.conn.setsockopt(socket.IPPROTO_TCP, getattr(socket, opt), val)
        except Exception as e:
            self.logger.warning(f"Could not set keepalive options: {e}")

    def _send_init_commands(self):
        """Send initialization commands to the device."""
        hioki_id = self.device["hioki_id"]

        try:
            for cmd in W610Server.AUTOINIT:
                payload = cmd.encode("ascii") + b"\r\n"
                self.conn.sendall(payload)
                self.logger.debug(f"Sent to {hioki_id}: {cmd}")
                time.sleep(0.05)  # Small delay between commands
        except Exception as e:
            self.logger.error(f"Error sending init commands to {hioki_id}: {e}")
            raise

    def _listen_for_readings(self):
        """Listen for incoming readings from the device."""
        hioki_id = self.device["hioki_id"]
        buffer = bytearray()

        while not self.stop_event.is_set() and not self._local_stop.is_set():
            try:
                # Set a short timeout so we can check stop events
                self.conn.settimeout(1.0)
                chunk = self.conn.recv(4096)

                if not chunk:
                    self.logger.info(f"W610 device {hioki_id} closed connection")
                    break

                # Debug: log raw data
                self.logger.debug(
                    f"RX from {hioki_id} - {len(chunk)} bytes HEX: {chunk.hex(' ')}"
                )

                buffer.extend(chunk)

                # Process complete lines
                while b"\n" in buffer:
                    nl_pos = buffer.find(b"\n")
                    line = bytes(buffer[: nl_pos + 1])
                    del buffer[: nl_pos + 1]

                    try:
                        text = line.decode("ascii", "ignore").strip()
                    except Exception:
                        text = ""

                    if text:
                        # Store raw response for command handling
                        self.response_queue.put(text)
                        # Process potential reading
                        self._process_reading(text)

            except socket.timeout:
                # Normal timeout, continue to check stop events
                continue
            except Exception as e:
                if not self.stop_event.is_set() and not self._local_stop.is_set():
                    self.logger.error(f"Error reading from W610 device {hioki_id}: {e}")
                break

    def _process_reading(self, text: str):
        """Process a reading from the device."""
        hioki_id = self.device["hioki_id"]

        try:
            if not text:
                return

            self.logger.debug(f"Raw reading from {hioki_id}: '{text}'")

            # Format the data (convert from scientific notation to milliohms)
            formatted = format_data(text)
            if formatted is None:
                # Not a numeric reading (likely a response to a command)
                self.logger.debug(
                    f"Non-reading response from {hioki_id}: '{text}'"
                )
                return

            # Create data structure including source IP
            data = {
                "value": formatted,
                "timestamp": datetime.now().isoformat(),
                "ip_address": self.addr[0],
            }

            # Update tracking
            self.last_reading_time = datetime.now()
            self.readings_received += 1

            # Update database
            db_service.update_device_reading(hioki_id, data)

            # Publish to MQTT
            client = mqtt_service.mqtt_client
            if client:
                topic = create_mqtt_topic(hioki_id)
                client.publish(topic, formatted, qos=1)
                self.logger.debug(
                    f"Published {hioki_id} reading {formatted} to {topic}"
                )
            else:
                self.logger.warning("MQTT client not available")

        except Exception as e:
            self.logger.error(f"Error processing reading from {hioki_id}: {e}")

    def stop(self):
        """Stop this client handler."""
        self.logger.info(f"Stopping handler for {self.device['hioki_id']}")
        self._local_stop.set()
        self._is_connected = False
        try:
            self.conn.close()
        except Exception:
            pass
