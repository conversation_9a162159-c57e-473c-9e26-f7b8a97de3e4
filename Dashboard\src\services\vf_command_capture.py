"""
VF Command Capture Service for HexMES
Captures Visual Factory commands from MQTT for all tool types.
"""

import json
import logging
import sqlite3
from datetime import datetime
from typing import Dict, Optional, List
import paho.mqtt.client as mqtt
import re

logger = logging.getLogger(__name__)

# MQTT Configuration
MQTT_BROKER = "vf-gateway-01"
MQTT_PORT = 1883
MQTT_USER = "visualfactory"
MQTT_PASSWORD = "Pr0terr@"


class VFCommandCapture:
    """Captures VF commands for all tool types before migration."""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.mqtt_client = None
        self.init_vf_tables()

    def init_vf_tables(self):
        """Initialize VF command capture tables."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Create VF commands table if it doesn't exist
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS vf_commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_type TEXT NOT NULL,
                    device_id TEXT NOT NULL,
                    tool_command TEXT,
                    work_station_name TEXT,
                    person_name TEXT,
                    test_item_name TEXT,
                    activity_id TEXT,
                    task_name TEXT,
                    route_name TEXT,
                    option_code TEXT,
                    lot_id TEXT,
                    unit_id TEXT,
                    work_order_number TEXT,
                    raw_command_json TEXT,
                    mqtt_topic TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed INTEGER DEFAULT 0
                )
            """
            )

            # Create index for quick lookups
            cursor.execute(
                """
                CREATE INDEX IF NOT EXISTS idx_vf_commands_device 
                ON vf_commands(device_type, device_id, timestamp)
            """
            )

            cursor.execute(
                """
                CREATE INDEX IF NOT EXISTS idx_vf_commands_unprocessed 
                ON vf_commands(processed, timestamp)
            """
            )

            conn.commit()
            logger.info("VF command capture tables initialized")

    def setup_mqtt_capture(self):
        """Setup MQTT client to capture ALL VF commands."""
        try:
            client_id = f"vf-command-capture-{int(datetime.now().timestamp())}"
            client = mqtt.Client(client_id=client_id)
            client.username_pw_set(MQTT_USER, MQTT_PASSWORD)
            client.on_connect = self.on_connect
            client.on_message = self.on_message
            client.connect(MQTT_BROKER, MQTT_PORT, 60)
            client.loop_start()
            self.mqtt_client = client

            logger.info("VF command capture MQTT client started")
            return client
        except Exception as e:
            logger.error(
                f"Failed to connect VF command capture to MQTT broker: {str(e)}"
            )
            return None

    def on_connect(self, client, userdata, flags, rc):
        """Handle MQTT connection and subscribe to ALL command topics."""
        logger.info(
            f"VF Command Capture connected to MQTT broker with result code {rc}"
        )

        # Subscribe to ALL possible command topics
        command_topics = [
            # Main VF command pattern for all tools
            "nomuda/gvl/tools/+/command",
            "nomuda/gvl/tools/GVB-+/command",
            # Specific device type patterns to catch everything
            "nomuda/gvl/tools/GVB-Hioki-+/command",
            "nomuda/gvl/tools/GVB-CTS-+/command",
            "nomuda/gvl/tools/GVB-Welder-+/command",
            "nomuda/gvl/tools/GVB-Custom-+/command",
            # Alternative patterns that might be used
            "visualfactory/commands/+",
            "vf/commands/+",
            # Legacy patterns just in case
            "Proterra/+/command",
            "HexMES/+/command",
        ]

        for topic_pattern in command_topics:
            client.subscribe(topic_pattern)
            logger.info(f"Subscribed to VF commands: {topic_pattern}")

        # Also subscribe to some result topics to understand the full flow
        result_topics = [
            "nomuda/gvl/tools/+/result/+",
            "nomuda/gvl/tools/GVB-+/result/+",
        ]

        for topic_pattern in result_topics:
            client.subscribe(topic_pattern)
            logger.info(f"Also monitoring results: {topic_pattern}")

    def on_message(self, client, userdata, msg):
        """Handle incoming MQTT messages."""
        topic = msg.topic
        payload = msg.payload.decode()

        try:
            if "/command" in topic:
                self.handle_vf_command(topic, payload)
            elif "/result/" in topic:
                # Just log results for debugging, don't store yet
                device_info = self.parse_device_from_topic(topic)
                if device_info:
                    logger.debug(
                        f"Result from {device_info['device_type']}-{device_info['device_id']}: {payload[:100]}..."
                    )

        except Exception as e:
            logger.error(f"Error processing VF message from {topic}: {str(e)}")

    def handle_vf_command(self, topic: str, payload: str):
        """Handle and store VF command."""
        logger.info(f"VF Command received on {topic}")
        logger.info(f"Command payload: {payload}")

        # Parse device info from topic
        device_info = self.parse_device_from_topic(topic)
        if not device_info:
            logger.warning(f"Could not parse device info from topic: {topic}")
            return

        # Parse the JSON command
        try:
            command_data = json.loads(payload)
            context = command_data.get("context", {})

            # Store the command with parsed fields
            self.store_vf_command(
                device_type=device_info["device_type"],
                device_id=device_info["device_id"],
                command_data=command_data,
                context=context,
                raw_json=payload,
                mqtt_topic=topic,
            )

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in VF command: {e}")
            # Store anyway with raw data
            self.store_vf_command(
                device_type=device_info["device_type"],
                device_id=device_info["device_id"],
                raw_json=payload,
                mqtt_topic=topic,
            )

    def parse_device_from_topic(self, topic: str) -> Optional[Dict[str, str]]:
        """Parse device information from MQTT topic."""
        # Updated patterns based on actual VF command structure
        patterns = [
            # GVB-DeviceType-SubType-DeviceID pattern (e.g., GVB-CTS-Manifold-L1M2)
            r"nomuda/gvl/tools/GVB-([^-]+)-([^-]+)-(.+?)/(command|result)",
            # GVB-DeviceType-DeviceID pattern (e.g., GVB-Hioki-RM3545-H001, GVB-Welder-WelderName)
            r"nomuda/gvl/tools/GVB-([^-]+)-(.+?)/(command|result)",
            # Simple device pattern
            r"nomuda/gvl/tools/([^/]+)/(command|result)",
            # Alternative patterns
            r"visualfactory/commands/(.*?)$",
            r"vf/commands/(.*?)$",
            r"Proterra/HexMES/(.*?)/(result|command)",
        ]

        for pattern in patterns:
            match = re.match(pattern, topic)
            if match:
                groups = match.groups()

                if len(groups) >= 4 and groups[1] in ["Manifold", "Enclosure"]:
                    # CTS pattern: GVB-CTS-Manifold-L1M2 or GVB-CTS-Enclosure-L2ENC
                    device_type_main = groups[0]  # CTS
                    device_subtype = groups[1]  # Manifold/Enclosure
                    device_id = groups[2]  # L1M2

                    device_type = f"{device_type_main}-{device_subtype}"

                    return {
                        "device_type": device_type,
                        "device_id": device_id,
                        "device_subtype": device_subtype,
                    }

                elif len(groups) >= 3:
                    # Standard patterns: GVB-Hioki-H001, GVB-Welder-WelderName
                    device_type_raw = groups[0]
                    device_id_part = groups[1]

                    # Handle Hioki special case: GVB-Hioki-RM3545-H001
                    if device_type_raw.lower() == "hioki":
                        if device_id_part.startswith("RM3545-"):
                            device_id = device_id_part.replace("RM3545-", "")
                        else:
                            device_id = device_id_part
                        device_type = "Hioki-RM3545"

                    elif device_type_raw.lower() == "welder":
                        device_id = device_id_part
                        device_type = "Welder"

                    elif device_type_raw.lower() == "custom":
                        device_id = device_id_part
                        device_type = "Custom"

                    else:
                        # Unknown device type, treat as custom
                        device_id = device_id_part
                        device_type = device_type_raw

                    return {"device_type": device_type, "device_id": device_id}

                elif len(groups) >= 1:
                    # Simple pattern - try to infer from device name
                    device_name = groups[0]

                    # Try to determine entity type from device name patterns
                    if device_name.startswith("H") and len(device_name) == 4:
                        # Looks like Hioki ID (H001, H012, etc.)
                        device_type = "Hioki-RM3545"
                        device_id = device_name
                    elif any(
                        marker in device_name.upper()
                        for marker in ["L1", "L2", "ENC", "CTS"]
                    ):
                        # Looks like CTS ID
                        device_type = "CTS-Manifold"  # Default
                        device_id = device_name
                    elif "weld" in device_name.lower():
                        device_type = "Welder"
                        device_id = device_name
                    else:
                        device_type = "Custom"
                        device_id = device_name

                    return {"device_type": device_type, "device_id": device_id}

        logger.warning(f"Could not parse device info from topic: {topic}")
        return None

    def store_vf_command(
        self,
        device_type: str,
        device_id: str,
        command_data: Dict = None,
        context: Dict = None,
        raw_json: str = None,
        mqtt_topic: str = None,
    ):
        """Store VF command in database."""

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Extract context fields if available
            tool_command = command_data.get("toolCommand") if command_data else None
            work_station_name = context.get("WorkStationName") if context else None
            person_name = context.get("PersonName") if context else None
            test_item_name = context.get("TestItemName") if context else None
            activity_id = context.get("ActivityID") if context else None
            task_name = context.get("TaskName") if context else None
            route_name = context.get("RouteName") if context else None
            option_code = context.get("OptionCode") if context else None
            lot_id = context.get("LotID") if context else None
            unit_id = context.get("UnitID") if context else None
            work_order_number = context.get("WorkOrderNumber") if context else None

            cursor.execute(
                """
                INSERT INTO vf_commands (
                    device_type, device_id, tool_command, work_station_name, person_name,
                    test_item_name, activity_id, task_name, route_name, option_code,
                    lot_id, unit_id, work_order_number, raw_command_json, mqtt_topic
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    device_type,
                    device_id,
                    tool_command,
                    work_station_name,
                    person_name,
                    test_item_name,
                    activity_id,
                    task_name,
                    route_name,
                    option_code,
                    lot_id,
                    unit_id,
                    work_order_number,
                    raw_json,
                    mqtt_topic,
                ),
            )

            command_id = cursor.lastrowid
            conn.commit()

            logger.info(f"Stored VF command {command_id} for {device_type}-{device_id}")
            logger.info(f"  Work Order: {work_order_number}")
            logger.info(f"  Unit ID: {unit_id}")
            logger.info(f"  Option Code: {option_code}")
            logger.info(f"  Station: {work_station_name}")
            logger.info(f"  Operator: {person_name}")

            return command_id

    def get_command_summary(self) -> Dict:
        """Get summary of captured commands."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Count by device type
            cursor.execute(
                """
                SELECT device_type, COUNT(*) as count
                FROM vf_commands 
                GROUP BY device_type
                ORDER BY count DESC
            """
            )
            device_type_counts = [dict(row) for row in cursor.fetchall()]

            # Recent commands
            cursor.execute(
                """
                SELECT device_type, device_id, tool_command, work_station_name, 
                       person_name, work_order_number, unit_id, timestamp
                FROM vf_commands 
                ORDER BY timestamp DESC 
                LIMIT 20
            """
            )
            recent_commands = [dict(row) for row in cursor.fetchall()]

            # Total count
            cursor.execute("SELECT COUNT(*) as total FROM vf_commands")
            total_count = cursor.fetchone()["total"]

            return {
                "total_commands": total_count,
                "device_type_counts": device_type_counts,
                "recent_commands": recent_commands,
            }

    def get_commands_for_device(
        self, device_type: str, device_id: str, limit: int = 50
    ) -> List[Dict]:
        """Get VF commands for a specific device."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT * FROM vf_commands 
                WHERE device_type = ? AND device_id = ?
                ORDER BY timestamp DESC 
                LIMIT ?
            """,
                (device_type, device_id, limit),
            )

            return [dict(row) for row in cursor.fetchall()]

    def stop(self):
        """Stop the VF command capture."""
        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()
            self.mqtt_client = None
            logger.info("VF command capture stopped")


def start_vf_command_monitoring(db_path: str) -> VFCommandCapture:
    """Start VF command monitoring before migration."""
    logger.info("Starting comprehensive VF command capture...")

    capture_system = VFCommandCapture(db_path)
    mqtt_client = capture_system.setup_mqtt_capture()

    if mqtt_client:
        logger.info("VF command capture is now running")
        logger.info(
            "Monitor all VF commands with: capture_system.get_command_summary()"
        )
        return capture_system
    else:
        logger.error("Failed to start VF command capture")
        return None


# Flask API endpoints to monitor VF commands
def add_vf_command_routes(app, capture_system: VFCommandCapture):
    """Add API routes to monitor VF commands."""

    @app.route("/api/vf/commands/summary")
    def vf_command_summary():
        """Get summary of captured VF commands."""
        summary = capture_system.get_command_summary()
        return app.response_class(
            response=json.dumps(summary, indent=2),
            status=200,
            mimetype="application/json",
        )

    @app.route("/api/vf/commands/device/<device_type>/<device_id>")
    def vf_commands_for_device(device_type, device_id):
        """Get VF commands for specific device."""
        limit = int(app.request.args.get("limit", 50))
        commands = capture_system.get_commands_for_device(device_type, device_id, limit)
        return app.response_class(
            response=json.dumps(commands, indent=2, default=str),
            status=200,
            mimetype="application/json",
        )

    @app.route("/api/vf/commands/recent")
    def recent_vf_commands():
        """Get recent VF commands across all devices."""
        limit = int(app.request.args.get("limit", 100))

        with sqlite3.connect(capture_system.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT * FROM vf_commands 
                ORDER BY timestamp DESC 
                LIMIT ?
            """,
                (limit,),
            )

            commands = [dict(row) for row in cursor.fetchall()]

        return app.response_class(
            response=json.dumps(commands, indent=2, default=str),
            status=200,
            mimetype="application/json",
        )

    logger.info("Added VF command monitoring API routes")
