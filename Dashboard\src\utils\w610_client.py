import socket
import threading
import time
import logging
from datetime import datetime
from typing import Dict, Optional

from .data_formatter import format_data, create_mqtt_topic
from ..services import db_service
from ..services import mqtt_service


class W610Client(threading.Thread):
    """Thread managing a keepalive TCP connection to a Hioki W610 device."""

    DEFAULT_PORT = 15000

    def __init__(self, info: Dict[str, str], stop_event: threading.Event):
        super().__init__(daemon=True)
        self.info = info
        self.stop_event = stop_event
        self._local_stop = threading.Event()
        self.logger = logging.getLogger(__name__)
        self.sock: Optional[socket.socket] = None

    def stop(self):
        """Request the thread to stop and close the socket."""
        self._local_stop.set()
        if self.sock:
            try:
                self.sock.shutdown(socket.SHUT_RDWR)
            except Exception:
                pass
            try:
                self.sock.close()
            except Exception:
                pass

    def send_command(self, command: str):
        """Send a command to the device if connected."""
        if not self.sock:
            raise RuntimeError("W610 connection not established")
        if not command.endswith("\n"):
            command += "\n"
        self.sock.sendall(command.encode("ascii", "ignore"))

    # ------------------------------------------------------------------
    def run(self):
        while not self.stop_event.is_set() and not self._local_stop.is_set():
            try:
                self._connect()
                self._listen()
            except Exception as e:
                self.logger.error(f"W610 {self.info.get('hioki_id')} error: {e}")
            finally:
                if self.sock:
                    try:
                        self.sock.close()
                    except Exception:
                        pass
                    self.sock = None
                if not self.stop_event.is_set() and not self._local_stop.is_set():
                    time.sleep(5)
        self.logger.info(f"W610 client for {self.info.get('hioki_id')} exiting")

    def _connect(self):
        ip = self.info.get("ip")
        port = int(self.info.get("port", self.DEFAULT_PORT))
        self.logger.info(f"Connecting to W610 {self.info.get('hioki_id')} at {ip}:{port}")
        self.sock = socket.create_connection((ip, port), timeout=5)
        # Enable TCP keepalive if available
        try:
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
        except Exception:
            pass
        db_service.update_device_status_by_hioki_id(self.info["hioki_id"], "online")

    def _listen(self):
        buffer = b""
        while not self.stop_event.is_set() and not self._local_stop.is_set():
            try:
                chunk = self.sock.recv(4096)
                if not chunk:
                    break
                buffer += chunk
                while b"\n" in buffer:
                    line, buffer = buffer.split(b"\n", 1)
                    text = line.decode("ascii", "ignore").strip()
                    if not text:
                        continue
                    self._handle_line(text)
            except Exception:
                break
        db_service.update_device_status_by_hioki_id(self.info["hioki_id"], "offline")

    def _handle_line(self, text: str):
        formatted = format_data(text)
        if formatted is None:
            return
        data = {"value": formatted, "timestamp": datetime.now().isoformat()}
        try:
            db_service.update_device_reading(self.info["hioki_id"], data)
            client = mqtt_service.mqtt_client
            if client:
                client.publish(create_mqtt_topic(self.info["hioki_id"]), formatted, qos=1)
        except Exception as e:
            self.logger.error(f"Failed to process reading from {self.info.get('hioki_id')}: {e}")
