import telnetlib
import time
import threading
import logging
import re
import sqlite3
from typing import Optional, Dict


def parse_cts_line(line: str) -> Optional[Dict[str, str]]:
    """Parse a line of CTS output into fields.

    Returns a dictionary with keys ``pressureloss`` and ``testpressure`` plus the
    raw line if successful. ``None`` is returned for lines that should be
    ignored.
    """
    if "*" in line or "x01" in line:
        return None

    parts = line.split()
    if not parts:
        return None

    def search(pattern: str) -> int:
        for idx, val in enumerate(parts):
            if re.fullmatch(pattern, val):
                return idx
        return -1

    def contains(substr: str) -> int:
        for idx, val in enumerate(parts):
            if substr in val:
                return idx
        return -1

    msg1_idx = search(r"dpsig")
    msg2_idx = search(r"psig")
    msg3_idx = contains("|GVB|")

    pressureloss = parts[msg1_idx - 1] if msg1_idx > 0 else None
    testpressure = parts[msg2_idx - 1] if msg2_idx > 0 else None
    serial = parts[msg3_idx] if msg3_idx != -1 else None

    return {
        "pressureloss": pressureloss,
        "testpressure": testpressure,
        "serial": serial,
        "raw": line,
    }


class CTSTester(threading.Thread):
    """Thread managing a connection to a single CTS tester."""

    def __init__(self, info: Dict[str, str], mqtt_client, db_path: str, stop_event: threading.Event):
        super().__init__(daemon=True)
        self.info = info
        self.mqtt_client = mqtt_client
        self.db_path = db_path
        self.stop_event = stop_event
        self.logger = logging.getLogger(__name__)
        self.telnet = None

    def run(self):
        while not self.stop_event.is_set():
            try:
                self._connect()
                self._listen()
            except Exception as e:
                self.logger.error(f"CTS {self.info.get('name')} error: {e}")
            finally:
                if self.telnet:
                    try:
                        self.telnet.close()
                    except Exception:
                        pass
                if not self.stop_event.is_set():
                    time.sleep(5)

    def _connect(self):
        self.telnet = telnetlib.Telnet(self.info["ip"], int(self.info["port"]), timeout=5)
        time.sleep(0.5)
        self.telnet.write(b"4\n")
        time.sleep(0.5)
        try:
            resp = self.telnet.read_very_eager().decode("utf-8", errors="ignore")
            if "Undefined command '4' entered" in resp:
                self.logger.info("CTS already connected to data stream")
        except Exception:
            pass

    def _listen(self):
        while not self.stop_event.is_set():
            try:
                line = self.telnet.read_until(b"\n", timeout=1)
            except EOFError:
                break
            if not line:
                continue
            text = line.decode("utf-8", errors="ignore").strip()
            if not text:
                continue
            data = parse_cts_line(text)
            if data:
                self._publish(data)
                self._update_db(data)

    def _publish(self, data: Dict[str, str]):
        prefix = (
            "nomuda/gvl/tools/GVB-CTS-Manifold"
            if self.info.get("cts_type") == "manifold"
            else "nomuda/gvl/tools/GVB-CTS-Enclosure"
        )
        base = f"{prefix}/{self.info.get('cts_id') or self.info.get('name')}"
        if not self.mqtt_client:
            return
        if data.get("pressureloss") is not None:
            self.mqtt_client.publish(f"{base}/result/pressureloss", data["pressureloss"], qos=1)
        if data.get("testpressure") is not None:
            self.mqtt_client.publish(f"{base}/result/testpressure", data["testpressure"], qos=1)
        self.mqtt_client.publish(f"{base}/result/test", data["raw"], qos=1)

    def _update_db(self, data: Dict[str, str]):
        from ..services import db_service
        from ..app import socketio  # local import to emit events
        try:
            db_service.update_cts_reading(self.info["id"], data)
            socketio.emit(
                "cts_update",
                {
                    "cts_id": self.info["id"],
                    "pressureloss": data.get("pressureloss"),
                    "testpressure": data.get("testpressure"),
                    "raw": data.get("raw"),
                },
            )
        except Exception as e:
            self.logger.error(f"Failed to update CTS DB: {e}")
