from flask import (
	Flask,
	render_template,
	request,
	jsonify,
	redirect,
	url_for,
	session,
	flash,
)
from flask_socketio import SocketIO
import requests
import json
import threading
import logging
import sqlite3
from datetime import datetime, timedelta
import time
import os
import signal
import atexit
from functools import wraps
from dotenv import load_dotenv
from .services.vf_command_capture import (
	start_vf_command_monitoring,
	add_vf_command_routes,
)

import os
import sys
import secrets
from typing import Dict, Optional

# Ensure the project root is on the Python path so ``ldap_utils`` can be found
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

from ldap_utils import authenticate
from .utils.data_formatter import (
	create_mqtt_topic,
	validate_hioki_number,
	is_valid_reading,
	format_data,
)
from .utils.cts import CTSTester
from .utils.w610_server import W610Server
from .services import db_service, mqtt_service


import datetime as dt

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
ROOT_DIR = os.path.abspath(os.path.join(BASE_DIR, ".."))
load_dotenv(os.path.join(ROOT_DIR, ".env"))
app = Flask(
	__name__,
	template_folder=os.path.join(BASE_DIR, "templates"),
	static_folder=os.path.join(BASE_DIR, "static"),
)
app.config["SECRET_KEY"] = "hexmes-dashboard-secret-key"
print("Starting VF command capture...")
vf_capture_system = start_vf_command_monitoring(DB_PATH)

if vf_capture_system:
	print("✓ VF command capture started successfully")
	# Add API routes for monitoring VF commands
	add_vf_command_routes(app, vf_capture_system)
	print("✓ VF command monitoring APIs added")

	# Store reference for shutdown
	app.vf_capture_system = vf_capture_system
else:
	print("✗ Failed to start VF command capture")

app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(hours=12)
socketio = SocketIO(app, cors_allowed_origins="*", async_mode="threading")
W610_SERVER_HOST = os.environ.get("W610_SERVER_HOST", "0.0.0.0")
W610_SERVER_PORT = int(os.environ.get("W610_SERVER_PORT", "15000"))
w610_server: Optional[W610Server] = None
w610_server_lock = threading.Lock()
# Flag to toggle token authentication for API endpoints. Set the
# ``AUTH_ENABLED`` environment variable to ``true`` to re-enable it.
AUTH_ENABLED = os.environ.get("AUTH_ENABLED", "false").lower() in (
	"1",
	"true",
	"yes",
	"on",
)


@app.context_processor
def inject_globals():
	return {"current_year": dt.datetime.now().year, "version": "0.2.6-THWI"}


# ---------------------------------------------------------------------------
# API token utilities
# ---------------------------------------------------------------------------


def create_api_token(username, expire_minutes=None):
	"""Generate and store an API token for the given user."""
	token = secrets.token_hex(32)
	expires_at = None
	if expire_minutes is not None:
		expires_at = (
			datetime.now(datetime.timezone.utc) + timedelta(minutes=expire_minutes)
		).isoformat()

	with sqlite3.connect(DB_PATH) as conn:
		cursor = conn.cursor()
		cursor.execute(
			"INSERT INTO api_tokens (token, username, expires_at) VALUES (?, ?, ?)",
			(token, username, expires_at),
		)
		conn.commit()
	return token


def validate_api_token(token):
	"""Validate a token and return the associated username if valid."""
	if not token:
		return None

	with sqlite3.connect(DB_PATH) as conn:
		cursor = conn.cursor()
		cursor.execute(
			"SELECT username, expires_at FROM api_tokens WHERE token = ?",
			(token,),
		)
		row = cursor.fetchone()
		if not row:
			return None

		username, expires_at = row

		# Tokens without an expiry date never expire
		if expires_at is None:
			return username

		try:
			if datetime.fromisoformat(expires_at) >= datetime.now(
				datetime.timezone.utc
			):
				return username
		except Exception:
			pass

	return None


def login_required(f):
	@wraps(f)
	def decorated_function(*args, **kwargs):
		if not session.get("username"):
			return redirect(url_for("login", next=request.url))
		return f(*args, **kwargs)

	return decorated_function


@app.before_request
def require_login():
	if request.path.startswith("/static/") or request.path.startswith("/socket.io/"):
		return
	if request.endpoint in ("login", "logout", "issue_token"):
		return

	# If authentication is disabled, allow API requests through without checks
	if not AUTH_ENABLED and request.path.startswith("/api/"):
		return

	# allow API access via token
	token = None
	auth_header = request.headers.get("Authorization", "")
	if auth_header.startswith("Bearer "):
		token = auth_header[len("Bearer ") :]
	if not token:
		token = request.args.get("access_token")
	username = validate_api_token(token) if token else None
	if username:
		request.api_user = username
		return

	if not session.get("username"):
		if request.path.startswith("/api/"):
			return jsonify({"error": "Unauthorized"}), 401
		return redirect(url_for("login", next=request.url))

	# check session expiration (12 hours)
	login_time_str = session.get("login_time")
	if login_time_str:
		try:
			login_time = datetime.fromisoformat(login_time_str)
			if datetime.utcnow() - login_time > timedelta(hours=12):
				session.pop("username", None)
				session.pop("login_time", None)
				if request.path.startswith("/api/"):
					return jsonify({"error": "Session expired"}), 401
				flash("Session expired. Please log in again.", "warning")
				return redirect(url_for("login", next=request.url))
		except Exception:
			session.pop("username", None)
			session.pop("login_time", None)
			if request.path.startswith("/api/"):
				return jsonify({"error": "Session expired"}), 401
			return redirect(url_for("login", next=request.url))


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Control flag for background threads
running = True
# Event used to signal threads to exit
stop_event = threading.Event()
# Store references to background threads for clean shutdown
background_threads = []
cts_threads = []
w610_server: Optional[W610Server] = None

# MQTT Configuration
MQTT_BROKER = "vf-gateway-01"
MQTT_PORT = 1883
MQTT_USER = "visualfactory"
MQTT_PASSWORD = "Pr0terr@"
MQTT_TOPIC_PREFIX = "Proterra/Greenville/GVB/Pack/Hioki-Bridge/"
W610_TOPIC_PREFIX = "Proterra/HexMES/Hioki/v1/"
CUSTOM_MQTT = ""
MQTT_CTS = ""

mqtt_client = None
# Database handled by service layer
DB_PATH = db_service.DB_PATH
db_service.init_db()

# Initialize MQTT using the service
mqtt_client = mqtt_service.setup_mqtt(socketio)


# Database helper aliases
get_bridges = db_service.get_bridges
get_bridge = db_service.get_bridge
get_cts = db_service.get_cts
get_cts_by_id = db_service.get_cts_by_id
add_cts = db_service.add_cts
add_bridge = db_service.add_bridge
update_bridge_status = db_service.update_bridge_status
update_cts_status = db_service.update_cts_status
update_cts_reading = db_service.update_cts_reading
update_device_status_by_hioki_id = db_service.update_device_status_by_hioki_id
get_devices = db_service.get_devices
update_device_reading = db_service.update_device_reading
get_device_history = db_service.get_device_history
find_bridge_by_ip_port = db_service.find_bridge_by_ip_port
add_device_record = db_service.add_device_record
remove_device_by_hioki_id = db_service.remove_device_by_hioki_id
get_serial_devices = db_service.get_serial_devices
get_w610_devices = db_service.get_w610_devices
add_custom_device = db_service.add_custom_device
get_custom_devices = db_service.get_custom_devices
update_device_testing_status = db_service.update_device_testing_status
get_db_connection = db_service.get_db_connection
add_welder = db_service.add_welder
get_welders = db_service.get_welders
get_welder_by_id = db_service.get_welder_by_id
set_welder_name = db_service.set_welder_name
update_welder_status = db_service.update_welder_status
update_welder_data = db_service.update_welder_data
remove_welder = db_service.remove_welder
get_last_reading = db_service.get_last_reading
# Routes


@app.route("/login", methods=["GET", "POST"])
def login():
	next_url = request.args.get("next")
	if request.method == "POST":
		username = request.form.get("username")
		password = request.form.get("password")
		if authenticate(username, password):
			session["username"] = username
			session["login_time"] = datetime.utcnow().isoformat()
			session.permanent = False
			return redirect(next_url or url_for("index"))
		else:
			flash("Invalid credentials", "danger")
	return render_template("login.html")


@app.route("/api/token", methods=["POST"])
def issue_token():
	"""Return an API token given valid credentials."""
	data = request.json or {}
	username = data.get("username")
	password = data.get("password")
	if not username or not password:
		return jsonify({"error": "Username and password required"}), 400
	if not authenticate(username, password):
		return jsonify({"error": "Invalid credentials"}), 401

	token = create_api_token(username)
	return jsonify({"token": token})


@app.route("/logout")
def logout():
	session.pop("username", None)
	session.pop("login_time", None)
	return redirect(url_for("login"))


@app.route("/")
def index():
	bridges = get_bridges()
	bridge_devices = get_devices()
	serial_devices = get_serial_devices()
	w610_devices = get_w610_devices()
	cts_devices = get_cts()
	welders = get_welders()
	custom_devices = get_custom_devices()
	return render_template(
		"index.html",
		bridges=bridges,
		devices=bridge_devices,
		serial_devices=serial_devices,
		w610_devices=w610_devices,
		cts_devices=cts_devices,
		welders=welders,
		custom_devices=custom_devices,
	)


@app.route("/bridges")
def list_bridges():
	bridges = get_bridges()
	return render_template("bridges.html", bridges=bridges)


@app.route("/bridges/add", methods=["GET", "POST"])
def add_bridge_route():
	if request.method == "POST":
		name = request.form.get("name")
		ip = request.form.get("ip")
		port = request.form.get("port", 5000)

		if name and ip:
			bridge_id = add_bridge(name, ip, port)
			return redirect(url_for("bridge_detail", bridge_id=bridge_id))

	return render_template("add_bridge.html")


@app.route("/bridges/<int:bridge_id>")
def bridge_detail(bridge_id):
	bridge = get_bridge(bridge_id)
	if not bridge:
		return "Bridge not found", 404

	devices = get_devices(bridge_id)

	# Get available COM ports from the bridge
	com_ports = []
	bridge_reachable = True
	try:
		response = requests.get(
			f"http://{bridge['ip']}:{bridge['port']}/api/get_ports", timeout=5
		)
		if response.status_code == 200:
			com_ports = response.json().get("ports", [])
	except Exception as e:
		logger.error(f"Error fetching COM ports: {str(e)}")
		bridge_reachable = False

	return render_template(
		"bridge_detail.html",
		bridge=bridge,
		devices=devices,
		com_ports=com_ports,
		bridge_reachable=bridge_reachable,
	)


@app.route("/api/bridges/<int:bridge_id>/add_device", methods=["POST"])
def add_device_to_bridge(bridge_id):
	bridge = get_bridge(bridge_id)
	if not bridge:
		return jsonify({"status": "error", "message": "Bridge not found"}), 404

	data = request.json
	port = data.get("port")
	hioki_id = data.get("hioki_id")
	tester_type = data.get("tester_type", "hioki")

	if not port or not hioki_id:
		return (
			jsonify({"status": "error", "message": "Missing port or hioki_id"}),
			400,
		)

	try:
		# Send request to bridge to add device
		response = requests.post(
			f"http://{bridge['ip']}:{bridge['port']}/api/add_device",
			json={"port": port, "hioki_id": hioki_id},
			timeout=5,
		)

		if response.status_code == 200 and response.json().get("status") == "success":
			# Add device to our database
			with sqlite3.connect(DB_PATH) as conn:
				cursor = conn.cursor()
				cursor.execute(
					"INSERT INTO devices (bridge_id, port, hioki_id, tester_type) VALUES (?, ?, ?, ?)",
					(bridge_id, port, hioki_id, tester_type),
				)
				conn.commit()

			return jsonify({"status": "success"})
		else:
			return (
				jsonify(
					{
						"status": "error",
						"message": "Failed to add device to bridge",
					}
				),
				500,
			)

	except Exception as e:
		logger.error(f"Error adding device: {str(e)}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/bridges/<int:bridge_id>/remove_device", methods=["POST"])
def remove_device_from_bridge(bridge_id):
	bridge = get_bridge(bridge_id)
	if not bridge:
		return jsonify({"status": "error", "message": "Bridge not found"}), 404

	data = request.json
	port = data.get("port")
	device_id = data.get("device_id")

	if not port and not device_id:
		return (
			jsonify({"status": "error", "message": "Missing port or device_id"}),
			400,
		)

	try:
		# Send request to bridge to remove device
		response = requests.delete(
			f"http://{bridge['ip']}:{bridge['port']}/api/remove_device/{port}",
			timeout=5,
		)

		if response.status_code == 200 and response.json().get("status") == "success":
			# Remove device from our database
			with sqlite3.connect(DB_PATH) as conn:
				cursor = conn.cursor()
				if device_id:
					cursor.execute("DELETE FROM devices WHERE id = ?", (device_id,))
				else:
					cursor.execute(
						"DELETE FROM devices WHERE bridge_id = ? AND port = ?",
						(bridge_id, port),
					)
				conn.commit()

			return jsonify({"status": "success"})
		else:
			return (
				jsonify(
					{
						"status": "error",
						"message": "Failed to remove device from bridge",
					}
				),
				500,
			)

	except Exception as e:
		logger.error(f"Error removing device: {str(e)}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route(
	"/api/bridges/<int:bridge_id>/devices/<int:device_id>/remove",
	methods=["POST"],
)
def remove_device_from_bridge_by_id(bridge_id, device_id):
	"""Remove a device using its ID. This route is used by the UI."""
	bridge = get_bridge(bridge_id)
	if not bridge:
		return jsonify({"status": "error", "message": "Bridge not found"}), 404

	device = get_device_by_id(device_id)
	if not device:
		return jsonify({"status": "error", "message": "Device not found"}), 404

	data = request.json or {}
	port = data.get("port") or device.get("port")

	if not port:
		return (
			jsonify({"status": "error", "message": "Missing device port"}),
			400,
		)

	try:
		response = requests.delete(
			f"http://{bridge['ip']}:{bridge['port']}/api/remove_device/{port}",
			timeout=5,
		)

		if response.status_code == 200 and response.json().get("status") == "success":
			with sqlite3.connect(DB_PATH) as conn:
				cursor = conn.cursor()
				cursor.execute("DELETE FROM devices WHERE id = ?", (device_id,))
				conn.commit()
			return jsonify({"status": "success"})
		else:
			return (
				jsonify(
					{
						"status": "error",
						"message": "Failed to remove device from bridge",
					}
				),
				500,
			)
	except Exception as e:
		logger.error(f"Error removing device by id: {str(e)}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/bridges/<int:bridge_id>/check_status", methods=["GET"])
def check_bridge_status(bridge_id):
	bridge = get_bridge(bridge_id)
	if not bridge:
		return jsonify({"status": "error", "message": "Bridge not found"}), 404

	try:
		# Check if bridge is running on localhost/same machine
		if bridge["ip"] in ["127.0.0.1", "localhost"] or bridge["ip"] == get_local_ip():
			# For local bridges, we can just check if the port is open
			import socket

			s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
			s.settimeout(1)
			result = s.connect_ex((bridge["ip"], bridge["port"]))
			s.close()

			if result == 0:
				update_bridge_status(bridge_id, "online")
				return jsonify({"status": "online"})
			else:
				update_bridge_status(bridge_id, "offline")
				return jsonify({"status": "offline"})
		else:
			# For remote bridges, query the bridge MQTT status
			import requests

			response = requests.get(
				f"http://{bridge['ip']}:{bridge['port']}/api/mqtt_status",
				timeout=2,
			)
			if response.status_code == 200:
				update_bridge_status(bridge_id, "online")
				return jsonify({"status": "online"})
			else:
				update_bridge_status(bridge_id, "offline")
				return jsonify({"status": "offline"})
	except Exception as e:
		logger.error(f"Error checking bridge status: {str(e)}")
		update_bridge_status(bridge_id, "offline")
		return jsonify({"status": "offline"})


@app.route("/api/bridges/<int:bridge_id>/get_devices", methods=["GET"])
def get_bridge_devices(bridge_id):
	bridge = get_bridge(bridge_id)
	if not bridge:
		return jsonify({"status": "error", "message": "Bridge not found"}), 404

	try:
		# Get devices from bridge
		response = requests.get(
			f"http://{bridge['ip']}:{bridge['port']}/api/devices", timeout=5
		)

		if response.status_code == 200:
			bridge_devices = response.json().get("devices", {})

			# Sync devices with our database
			with sqlite3.connect(DB_PATH) as conn:
				conn.row_factory = sqlite3.Row
				cursor = conn.cursor()

				# Get existing devices for this bridge
				cursor.execute(
					"SELECT * FROM devices WHERE bridge_id = ?", (bridge_id,)
				)
				db_devices = {
					device["port"]: dict(device) for device in cursor.fetchall()
				}

				# Add new devices
				for port, device_data in bridge_devices.items():
					if port not in db_devices:
						hioki_id = device_data.get("hioki_id")
						if hioki_id:
							cursor.execute(
								"INSERT INTO devices (bridge_id, port, hioki_id, tester_type) VALUES (?, ?, ?, ?)",
								(
									bridge_id,
									port,
									hioki_id,
									device_data.get("tester_type", "hioki"),
								),
							)

				# Remove devices that no longer exist on the bridge
				for port, device in db_devices.items():
					if port not in bridge_devices:
						cursor.execute(
							"DELETE FROM devices WHERE id = ?", (device["id"],)
						)

				conn.commit()

			return jsonify({"status": "success", "devices": bridge_devices})
		else:
			return (
				jsonify(
					{
						"status": "error",
						"message": "Failed to get devices from bridge",
					}
				),
				500,
			)

	except Exception as e:
		logger.error(f"Error getting devices: {str(e)}")
		return jsonify({"status": "error", "message": str(e)}), 500


# Background task to check bridge status periodically
def check_bridges_status():
	"""Background task to poll bridge status."""
	global running
	while not stop_event.is_set():
		try:
			bridges = get_bridges()
			for bridge in bridges:
				try:
					response = requests.get(
						f"http://{bridge['ip']}:{bridge['port']}/api/mqtt_status",
						timeout=2,
					)

					if response.status_code == 200:
						status_data = response.json()
						status = (
							"online"
							if status_data.get("connected", False)
							else "offline"
						)
					else:
						status = "offline"

				except requests.exceptions.RequestException:
					status = "offline"

				# Update bridge status
				update_bridge_status(bridge["id"], status)
				socketio.emit(
					"bridge_status_update",
					{"bridge_id": bridge["id"], "status": status},
				)

		except Exception as e:
			logger.error(f"Error in status check thread: {str(e)}")

		# Check every 30 seconds or exit sooner if requested
		stop_event.wait(30)
	logger.info("check_bridges_status thread exiting")


def update_bridge_devices_status():
	"""Background task to update device status from bridges."""
	global running
	while not stop_event.is_set():
		try:
			bridges = get_bridges()
			for bridge in bridges:
				try:
					# Skip bridges that are known to be offline
					if bridge["status"] == "offline":
						continue

					# Get device status from bridge
					logger.info(
						f"Fetching device status from bridge {bridge['id']} at {bridge['ip']}:{bridge['port']}"
					)
					response = requests.get(
						f"http://{bridge['ip']}:{bridge['port']}/api/devices",
						timeout=2,
					)

					if response.status_code == 200:
						devices_status = response.json().get("devices", {})
						logger.info(
							f"Received status for {len(devices_status)} devices from bridge {bridge['id']}"
						)
						logger.info(f"Device status data: {json.dumps(devices_status)}")

						# Update devices in our database
						with sqlite3.connect(DB_PATH) as conn:
							conn.row_factory = sqlite3.Row
							cursor = conn.cursor()

							# Get all devices for this bridge
							cursor.execute(
								"SELECT id, port, hioki_id FROM devices WHERE bridge_id = ?",
								(bridge["id"],),
							)
							db_devices = {d["port"]: d for d in cursor.fetchall()}
							logger.info(
								f"Found {len(db_devices)} devices in database for bridge {bridge['id']}"
							)

							# Update each device
							for port, status in devices_status.items():
								if port in db_devices:
									device = db_devices[port]
									device_status = (
										"active"
										if status.get("connected", False)
										else "inactive"
									)

									logger.info(
										f"Updating device {device['id']} (hioki_id: {device['hioki_id']}) with status: {device_status}, reading: {status.get('last_reading')}, timestamp: {status.get('last_update')}"
									)

									# Update device status
									cursor.execute(
										"""
										UPDATE devices 
										SET status = ?, last_reading = ?, last_update = ?, testing = ?
										WHERE id = ?
										""",
										(
											device_status,
											status.get("last_reading"),
											status.get("last_update"),
											(
												1
												if status.get("test_mode", False)
												else 0
											),
											device["id"],
										),
									)

									# Emit update via Socket.IO
									socketio.emit(
										"device_update",
										{
											"device_id": device["id"],
											"hioki_id": device["hioki_id"],
											"status": device_status,
											"reading": status.get("last_reading"),
											"timestamp": status.get("last_update"),
											"testing": status.get("test_mode", False),
											"disconnected_at": status.get(
												"disconnected_at"
											),
										},
									)

							conn.commit()

				except requests.exceptions.RequestException:
					# Bridge is probably offline
					update_bridge_status(bridge["id"], "offline")
					socketio.emit(
						"bridge_status_update",
						{"bridge_id": bridge["id"], "status": "offline"},
					)
				except Exception as e:
					logger.error(
						f"Error updating devices for bridge {bridge['id']}: {str(e)}"
					)

		except Exception as e:
			logger.error(f"Error in device status update thread: {str(e)}")

		# Check every 5 seconds or exit sooner if requested
		stop_event.wait(5)
	logger.info("update_bridge_devices_status thread exiting")


def start_background_tasks():
	"""Start background worker threads"""
	global background_threads, w610_server

	t1 = threading.Thread(target=check_bridges_status, daemon=True)
	t2 = threading.Thread(target=update_bridge_devices_status, daemon=True)
	t1.start()
	t2.start()
	background_threads = [t1, t2]

	# Start CTS tester threads for each configured CTS device
	for cts in get_cts():
		t = CTSTester(cts, mqtt_client, DB_PATH, stop_event)
		t.start()
		cts_threads.append(t)
	background_threads.extend(cts_threads)

	# Start W610 server ONLY if not already running
	w610_devices = db_service.get_w610_devices()
	if w610_devices:
		# Check if server is already running
		if w610_server and w610_server.is_alive():
			logger.info("W610 server already running, skipping startup")
		else:
			try:
				w610_server = W610Server(stop_event, W610_SERVER_HOST, W610_SERVER_PORT)
				w610_server.start()
				background_threads.append(w610_server)
				logger.info(
					f"Started W610 server listening on {W610_SERVER_HOST}:{W610_SERVER_PORT}"
				)
			except Exception as e:
				logger.error(f"Failed to start W610 server: {e}")
	else:
		logger.info("No W610 devices configured, W610 server not started")


background_tasks_started = False


@app.before_request
def before_request_func():
	global background_tasks_started
	if not background_tasks_started:
		# Start the background tasks
		start_background_tasks()
		background_tasks_started = True


def get_local_ip_addresses():
	"""Return a list of IPv4 addresses assigned to the host."""
	ip_addresses = []
	try:
		hostname = socket.gethostname()
		primary_ip = socket.gethostbyname(hostname)
		ip_addresses.append(primary_ip)

		try:
			addresses = socket.getaddrinfo(hostname, None)
			for addr in addresses:
				ip = addr[4][0]
				if (
					not ip.startswith("127.")
					and ":" not in ip
					and ip not in ip_addresses
				):
					ip_addresses.append(ip)
		except Exception:
			pass
	except Exception:
		ip_addresses.append("127.0.0.1")

	return ip_addresses


def get_local_ip():
	"""Get the primary local IP address.

	Similar to the bridge helper, this avoids relying on external
	connectivity by connecting to the non-routable ``**************``.
	If that fails, the function falls back to any non-loopback address
	discovered by :func:`get_local_ip_addresses`.
	"""

	try:
		s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
		s.connect(("**************", 1))
		ip = s.getsockname()[0]
		s.close()
		if not ip.startswith("127."):
			return ip
	except Exception:
		pass

	try:
		for addr in get_local_ip_addresses():
			if not addr.startswith("127."):
				return addr
	except Exception:
		pass

	return "127.0.0.1"


def get_device_by_id(device_id):
	"""Get a device by its ID"""
	with sqlite3.connect(DB_PATH) as conn:
		conn.row_factory = sqlite3.Row
		cursor = conn.cursor()
		cursor.execute("SELECT * FROM devices WHERE id = ?", (device_id,))
		result = cursor.fetchone()
		return dict(result) if result else None


def get_db_connection():
	"""Create a connection to the SQLite database"""
	conn = sqlite3.connect(DB_PATH)
	conn.row_factory = sqlite3.Row
	return conn


def shutdown_handler():
	"""Gracefully stop background tasks and MQTT client"""
	global running, mqtt_client, w610_server
	if stop_event.is_set():
		return
	logger.info("Dashboard shutting down...")
	running = False
	stop_event.set()
		if stop_event.is_set():
		return
	logger.info("Dashboard shutting down...")
	running = False
	stop_event.set()

	# ADD THIS LINE - Stop VF capture
	if hasattr(app, 'vf_capture_system') and app.vf_capture_system:
		app.vf_capture_system.stop()
	# Stop W610 server
	if w610_server:
		w610_server.stop()

	for t in background_threads:
		try:
			if t.is_alive():
				t.join(timeout=1.0)
		except Exception:
			pass

	if mqtt_client:
		try:
			mqtt_client.loop_stop()
			mqtt_client.disconnect()
		except Exception as e:
			logger.error(f"Error during MQTT shutdown: {e}")
		mqtt_client = None

	logger.info("Shutdown complete")

	# Exit the application once cleanup is finished
	raise SystemExit


# Register graceful shutdown handlers
atexit.register(shutdown_handler)
for sig in (signal.SIGTERM, signal.SIGINT):
	signal.signal(sig, lambda signum, frame: shutdown_handler())


@app.route(
	"/api/bridges/<int:bridge_id>/devices/<int:device_id>/test_reading",
	methods=["POST"],
)
def test_device_reading(bridge_id, device_id):
	bridge = get_bridge(bridge_id)
	device = get_device_by_id(device_id)

	if not bridge or not device:
		return jsonify({"status": "error", "message": "Bridge or device not found"})

	port = request.json.get("port")

	try:
		response = requests.post(
			f"http://{bridge['ip']}:{bridge['port']}/api/test_mode/{port}",
			json={"enable": True},
			timeout=5,
		)

		if response.status_code == 200:
			# Update device testing status in database
			update_device_testing_status(device_id, True)
			return jsonify({"status": "success"})
		else:
			return jsonify(
				{"status": "error", "message": "Failed to start test reading"}
			)
	except Exception as e:
		logger.error(f"Error starting test reading: {str(e)}")
		return jsonify({"status": "error", "message": str(e)})


@app.route("/api/w610/server_status", methods=["GET"])
def w610_server_status():
	"""Get W610 server status."""
	global w610_server

	if w610_server and w610_server.is_alive():
		return jsonify(
			{
				"status": "running",
				"host": W610_SERVER_HOST,
				"port": W610_SERVER_PORT,
				"connected_devices": len(w610_server.client_handlers),
			}
		)
	else:
		return jsonify(
			{
				"status": "stopped",
				"host": W610_SERVER_HOST,
				"port": W610_SERVER_PORT,
				"connected_devices": 0,
			}
		)


@app.route(
	"/api/bridges/<int:bridge_id>/devices/<int:device_id>/stop_test_reading",
	methods=["POST"],
)
def stop_test_device_reading(bridge_id, device_id):
	bridge = get_bridge(bridge_id)
	device = get_device_by_id(device_id)

	if not bridge or not device:
		return jsonify({"status": "error", "message": "Bridge or device not found"})

	port = request.json.get("port")

	try:
		response = requests.post(
			f"http://{bridge['ip']}:{bridge['port']}/api/test_mode/{port}",
			json={"enable": False},
			timeout=5,
		)

		if response.status_code == 200:
			# Update device testing status in database
			update_device_testing_status(device_id, False)
			return jsonify({"status": "success"})
		else:
			return jsonify(
				{"status": "error", "message": "Failed to stop test reading"}
			)
	except Exception as e:
		logger.error(f"Error stopping test reading: {str(e)}")
		return jsonify({"status": "error", "message": str(e)})


def update_device_testing_status(device_id, testing):
	"""Update the testing status of a device in the database"""
	conn = get_db_connection()
	cursor = conn.cursor()
	cursor.execute(
		"UPDATE devices SET testing = ? WHERE id = ?",
		(1 if testing else 0, device_id),
	)

	# Get the device info to emit the update
	cursor.execute("SELECT hioki_id FROM devices WHERE id = ?", (device_id,))
	device = cursor.fetchone()

	conn.commit()
	conn.close()

	# Emit the testing status update via Socket.IO
	if device:
		socketio.emit(
			"device_testing_update",
			{"hioki_id": device["hioki_id"], "testing": testing},
		)


def refresh_devices_from_bridges():
	"""Query all bridges for their connected devices and update the DB."""
	bridges = get_bridges()
	for bridge in bridges:
		try:
			response = requests.get(
				f"http://{bridge['ip']}:{bridge['port']}/api/devices", timeout=2
			)
			if response.status_code == 200:
				devices_status = response.json().get("devices", {})
				with sqlite3.connect(DB_PATH) as conn:
					conn.row_factory = sqlite3.Row
					cursor = conn.cursor()
					for port, dev in devices_status.items():
						cursor.execute(
							"SELECT id FROM devices WHERE bridge_id = ? AND port = ?",
							(bridge["id"], port),
						)
						row = cursor.fetchone()
						status = "active" if dev.get("connected", False) else "inactive"
						if row:
							cursor.execute(
								"""
								UPDATE devices
								SET hioki_id = ?, status = ?, last_reading = ?, last_update = ?, testing = ?
								WHERE id = ?
								""",
								(
									dev.get("hioki_id"),
									status,
									dev.get("last_reading"),
									dev.get("last_update"),
									1 if dev.get("test_mode") else 0,
									row["id"],
								),
							)
						else:
							cursor.execute(
								"""
								INSERT INTO devices (bridge_id, port, hioki_id, status, last_reading, last_update, testing)
								VALUES (?, ?, ?, ?, ?, ?, ?)
								""",
								(
									bridge["id"],
									port,
									dev.get("hioki_id"),
									status,
									dev.get("last_reading"),
									dev.get("last_update"),
									1 if dev.get("test_mode") else 0,
								),
							)
					conn.commit()
		except Exception as e:
			logger.error(
				f"Error refreshing devices from bridge {bridge['id']}: {str(e)}"
			)


@app.route("/api/bridges/<int:bridge_id>/remove", methods=["POST"])
def remove_bridge(bridge_id):
	try:
		conn = get_db_connection()
		cursor = conn.cursor()

		# First, remove all devices associated with this bridge
		cursor.execute("DELETE FROM devices WHERE bridge_id = ?", (bridge_id,))

		# Then, remove the bridge
		cursor.execute("DELETE FROM bridges WHERE id = ?", (bridge_id,))

		conn.commit()
		conn.close()

		return jsonify({"status": "success"})
	except Exception as e:
		logger.error(f"Error removing bridge: {str(e)}")
		return jsonify({"status": "error", "message": str(e)})


@app.context_processor
def inject_globals():
	return {
		"current_year": dt.datetime.now().year,
		"version": "0.2.6-THWI",
		"w610_server_host": W610_SERVER_HOST,
		"w610_server_port": W610_SERVER_PORT,
	}


@app.route("/api/devices")
def api_get_devices():
	"""Return a consolidated list of all known devices and bridges.

	This endpoint acts as a one‑stop source for the dashboard. It refreshes the
	device list from each bridge, then collects information about bridges,
	standard devices, CTS testers and any custom devices.  Each returned item
	includes an ``entity_type`` field so the frontend can differentiate between
	bridges, Hioki devices, CTS testers, etc.
	"""

	# Ensure our database reflects the latest state of attached bridges
	refresh_devices_from_bridges()

	# Standard Hioki devices (serial, HTTP, W610, etc.)
	devices = get_devices()
	for d in devices:
		d["entity_type"] = "device"
		d["type"] = d.get("tester_type") or "unknown"

	# Bridge information
	bridges = get_bridges()
	for b in bridges:
		b = dict(b)
		b["entity_type"] = "bridge"
		b["type"] = "bridge"
		# Explicitly report connection method for bridge entries
		b["conn_method"] = "bridge"

	# CTS testers
	cts_list = get_cts()
	for c in cts_list:
		c = dict(c)
		c["entity_type"] = "cts"
		c["type"] = "cts"

	welders_list = get_welders()
	for w in welders_list:
		w["entity_type"] = "device"
		w["device_type"] = "ultrasonic"
		w["type"] = "ultrasonic"

	# Custom devices defined via the dashboard
	custom_devices = get_custom_devices()
	for cd in custom_devices:
		cd = dict(cd)
		cd["entity_type"] = "custom"
		cd["type"] = cd.get("in_protocol") or "custom"

	all_entities = devices + bridges + cts_list + welders_list + custom_devices
	return jsonify(all_entities)


@app.route("/api/bridges/<int:bridge_id>/devices")
def api_get_bridge_devices(bridge_id):
	"""API endpoint to get devices for a specific bridge"""
	conn = get_db_connection()
	conn.row_factory = sqlite3.Row
	cursor = conn.cursor()

	cursor.execute(
		"""
		SELECT * FROM devices 
		WHERE bridge_id = ?
		ORDER BY name
		""",
		(bridge_id,),
	)

	devices = [dict(row) for row in cursor.fetchall()]
	conn.close()

	return jsonify(devices)


# Add these Socket.IO event handlers
@socketio.on("connect")
def handle_connect():
	logger.info(f"Client connected: {request.sid}")
	# Request device lists from all bridges
	bridges = get_bridges()
	for bridge in bridges:
		try:
			requests.get(
				f"http://{bridge['ip']}:{bridge['port']}/api/devices",
				timeout=2,
			)
		except Exception as e:
			logger.error(
				f"Error requesting devices from bridge {bridge['id']}: {str(e)}"
			)


# Helper functions for bridge/device management
def find_bridge_by_ip_port(ip, port):
	"""Find a bridge by IP and port"""
	with sqlite3.connect(DB_PATH) as conn:
		conn.row_factory = sqlite3.Row
		cursor = conn.cursor()
		cursor.execute("SELECT * FROM bridges WHERE ip = ? AND port = ?", (ip, port))
		bridge = cursor.fetchone()
		return dict(bridge) if bridge else None


def add_device_record(bridge_id, port, hioki_id):
	"""Add a device to a bridge in the database"""
	with sqlite3.connect(DB_PATH) as conn:
		cursor = conn.cursor()
		# Check if device already exists
		cursor.execute(
			"SELECT id FROM devices WHERE bridge_id = ? AND hioki_id = ?",
			(bridge_id, hioki_id),
		)
		device = cursor.fetchone()
		if not device:
			cursor.execute(
				"INSERT INTO devices (bridge_id, port, hioki_id) VALUES (?, ?, ?)",
				(bridge_id, port, hioki_id),
			)
			conn.commit()
			return cursor.lastrowid
		return None


def remove_device_by_hioki_id(hioki_id):
	"""Remove a device by its Hioki ID"""
	with sqlite3.connect(DB_PATH) as conn:
		cursor = conn.cursor()
		cursor.execute("DELETE FROM devices WHERE hioki_id = ?", (hioki_id,))
		conn.commit()
		return cursor.rowcount > 0


def get_serial_devices():
	with sqlite3.connect(DB_PATH) as conn:
		conn.row_factory = sqlite3.Row
		cursor = conn.cursor()
		cursor.execute(
			"""
			SELECT * FROM devices 
			WHERE device_type = 'serial'
			ORDER BY hioki_id
			"""
		)
		return [dict(row) for row in cursor.fetchall()]


def parse_timestamp(ts):
	"""Parse a timestamp from the database into a ``datetime`` object."""
	if not ts:
		return None
	try:
		return datetime.fromisoformat(ts)
	except Exception:
		try:
			return datetime.strptime(ts, "%Y-%m-%d %H:%M:%S")
		except Exception:
			return None


def get_w610_devices():
	"""Retrieve devices that send readings via W610.

	If a device was marked offline but the last update was more than 30 seconds
	ago, its status is reset to ``online``.
	"""
	with sqlite3.connect(DB_PATH) as conn:
		conn.row_factory = sqlite3.Row
		cursor = conn.cursor()
		cursor.execute(
			"""
			SELECT * FROM devices
			WHERE device_type = 'w610'
			ORDER BY hioki_id
			"""
		)
		devices = [dict(row) for row in cursor.fetchall()]

	now = datetime.now()
	to_update = []
	for device in devices:
		if device.get("status") == "offline":
			last_dt = parse_timestamp(device.get("last_update"))
			if last_dt and (now - last_dt).total_seconds() > 30:
				device["status"] = "online"
				to_update.append(device["id"])

	if to_update:
		with sqlite3.connect(DB_PATH) as conn:
			cursor = conn.cursor()
			cursor.executemany(
				"UPDATE devices SET status = 'online' WHERE id = ?",
				[(d,) for d in to_update],
			)
			conn.commit()

	return devices


def add_custom_device(name, ip, port, in_protocol, in_param, out_protocol, out_param):
	"""Add a custom device to the database"""
	with sqlite3.connect(DB_PATH) as conn:
		cursor = conn.cursor()
		cursor.execute(
			"""
			INSERT INTO custom_devices (name, ip, port, in_protocol, in_param, out_protocol, out_param)
			VALUES (?, ?, ?, ?, ?, ?, ?)
			""",
			(name, ip, port, in_protocol, in_param, out_protocol, out_param),
		)
		conn.commit()
		return cursor.lastrowid


def get_custom_devices():
	"""Retrieve all custom devices"""
	with sqlite3.connect(DB_PATH) as conn:
		conn.row_factory = sqlite3.Row
		cursor = conn.cursor()
		cursor.execute("SELECT * FROM custom_devices ORDER BY name")
		return [dict(row) for row in cursor.fetchall()]


@app.route("/serial/add", methods=["GET", "POST"])
def add_serial_route():
	if request.method == "POST":
		name = request.form.get("name")
		ip = request.form.get("ip")
		port = request.form.get("port", "23")  # Default port for Telnet
		tester_type = "hioki"

		if name and ip:
			with sqlite3.connect(DB_PATH) as conn:
				cursor = conn.cursor()
				cursor.execute(
					"INSERT INTO devices (hioki_id, ip, port, device_type, tester_type) VALUES (?, ?, ?, 'serial', ?)",
					(name, ip, port, tester_type),
				)
				conn.commit()
				device_id = cursor.lastrowid

			logger.info(f"Added serial device {name} at {ip}:{port}")
			return redirect(url_for("index"))

	return render_template("add_bridge.html")


@app.route("/w610/add", methods=["GET", "POST"])
def add_w610_route():
	"""Add a Hioki device that reports via W610."""
	global w610_server

	if request.method == "POST":
		name = request.form.get("name")
		ip = request.form.get("ip")
		tester_type = "hioki"

		if name and ip:
			with sqlite3.connect(DB_PATH) as conn:
				cursor = conn.cursor()
				cursor.execute(
					"INSERT INTO devices (hioki_id, ip, port, device_type, status, tester_type) VALUES (?, ?, ?, 'w610', 'offline', ?)",
					(name, ip, W610Server.DEFAULT_PORT, tester_type),
				)
				conn.commit()
				device_id = cursor.lastrowid

			# Start W610 server if it's not running (use same parameters as startup)
			if not w610_server or not w610_server.is_alive():
				try:
					if w610_server:
						w610_server.stop()

					w610_server = W610Server(
						stop_event, W610_SERVER_HOST, W610_SERVER_PORT
					)  # Use consistent parameters
					w610_server.start()
					logger.info("Started/restarted W610 server for new device")
				except Exception as e:
					logger.error(f"Failed to start/restart W610 server: {e}")
			else:
				logger.info("W610 server already running")

			logger.info(f"Added W610 Hioki device {name} at {ip}")
			return redirect(url_for("index"))

	return render_template("add_bridge.html")


@app.route("/api/w610/connected_devices", methods=["GET"])
def get_connected_w610_devices():
	"""Get list of currently connected W610 devices."""
	global w610_server

	if w610_server and w610_server.is_alive():
		connected_devices = w610_server.get_connected_devices()
		return jsonify(
			{
				"status": "success",
				"devices": connected_devices,
				"server_status": "running",
			}
		)
	else:
		return jsonify({"status": "success", "devices": {}, "server_status": "stopped"})


@app.route("/api/w610/<hioki_id>/send_command", methods=["POST"])
def send_command_to_w610_device(hioki_id):
	"""Send a command to a specific W610 device.

	Expected JSON payload:
	{
			"command": "FETCh?",  // SCPI command to send
			"wait_for_response": true  // Optional: whether to wait for response
	}
	"""
	global w610_server

	if not w610_server or not w610_server.is_alive():
		return (
			jsonify({"status": "error", "message": "W610 server is not running"}),
			503,
		)

	data = request.get_json()
	if not data or not data.get("command"):
		return jsonify({"status": "error", "message": "Command is required"}), 400

	command = data["command"].strip()

	# Validate the device exists in our database
	try:
		devices = db_service.get_w610_devices()
		device_found = any(d["hioki_id"] == hioki_id for d in devices)
		if not device_found:
			return (
				jsonify(
					{
						"status": "error",
						"message": f"Device {hioki_id} not found in configuration",
					}
				),
				404,
			)
	except Exception as e:
		logger.error(f"Error checking device existence: {e}")
		return jsonify({"status": "error", "message": "Database error"}), 500

	# Send the command
	try:
		response = w610_server.send_command_to_device(hioki_id, command)

		if response is None:
			return (
				jsonify(
					{
						"status": "error",
						"message": f"Failed to send command to {hioki_id} - device may be offline",
					}
				),
				404,
			)

		formatted = format_data(response) if is_valid_reading(response) else None
		logger.info(f"Command '{command}' sent to device {hioki_id}")
		return jsonify(
			{
				"status": "success",
				"command": command,
				"response": response,
				"reading": formatted,
			}
		)

	except Exception as e:
		logger.error(f"Error sending command to {hioki_id}: {e}")
		return jsonify({"status": "error", "message": f"Server error: {str(e)}"}), 500


@app.route("/api/w610/<hioki_id>/trigger_reading", methods=["POST"])
def trigger_w610_reading(hioki_id):
	"""Convenience endpoint to trigger a reading from a specific device."""
	global w610_server

	if not w610_server or not w610_server.is_alive():
		return (
			jsonify({"status": "error", "message": "W610 server is not running"}),
			503,
		)

	try:
		response = w610_server.send_command_to_device(hioki_id, "FETCh?")
		if response is None:
			return (
				jsonify(
					{
						"status": "error",
						"message": f"Device {hioki_id} is not connected",
					}
				),
				404,
			)

		formatted = format_data(response) if is_valid_reading(response) else None
		return jsonify(
			{"status": "success", "response": response, "reading": formatted}
		)
	except Exception as e:
		logger.error(f"Error triggering reading for {hioki_id}: {e}")
		return jsonify({"status": "error", "message": f"Server error: {str(e)}"}), 500


@app.route("/api/w610/<hioki_id>/get_info", methods=["POST"])
def get_w610_device_info(hioki_id):
	"""Get device identification information."""
	global w610_server

	if not w610_server or not w610_server.is_alive():
		return (
			jsonify({"status": "error", "message": "W610 server is not running"}),
			503,
		)

	# Send *IDN? command to get device identification
	try:
		response = w610_server.send_command_to_device(hioki_id, "*IDN?")

		if response is None:
			return (
				jsonify(
					{
						"status": "error",
						"message": f"Device {hioki_id} is not connected",
					}
				),
				404,
			)

		return jsonify(
			{
				"status": "success",
				"response": response,
			}
		)

	except Exception as e:
		logger.error(f"Error getting device info for {hioki_id}: {e}")
		return jsonify({"status": "error", "message": f"Server error: {str(e)}"}), 500


@app.route("/api/w610/<hioki_id>/connection_status", methods=["GET"])
def get_w610_connection_status(hioki_id):
	"""Get detailed connection status for a specific device."""
	global w610_server

	if not w610_server or not w610_server.is_alive():
		return jsonify(
			{"status": "disconnected", "message": "W610 server is not running"}
		)

	connected_devices = w610_server.get_connected_devices()
	device_info = connected_devices.get(hioki_id)

	if device_info:
		return jsonify({"status": "connected", "device_info": device_info})
	else:
		return jsonify(
			{
				"status": "disconnected",
				"message": f"Device {hioki_id} is not currently connected",
			}
		)


# Update the existing resend route to use the new command system
@app.route("/api/w610/<int:device_id>/resend", methods=["POST"])
def resend_w610_result(device_id):
	"""Send a FETCh? command to the specified W610 device."""
	# Get device info from database
	try:
		with sqlite3.connect(DB_PATH) as conn:
			conn.row_factory = sqlite3.Row
			cursor = conn.cursor()
			cursor.execute(
				"SELECT hioki_id FROM devices WHERE id = ? AND device_type = 'w610'",
				(device_id,),
			)
			device = cursor.fetchone()

			if not device:
				return jsonify({"status": "error", "message": "Device not found"}), 404

			hioki_id = device["hioki_id"]

	except Exception as e:
		logger.error(f"Database error in resend: {e}")
		return jsonify({"status": "error", "message": "Database error"}), 500

	# Send FETCh? command
	global w610_server
	if w610_server and w610_server.is_alive():
		response = w610_server.send_command_to_device(hioki_id, "FETCh?")
		if response is not None:
			formatted = format_data(response) if is_valid_reading(response) else None
			return jsonify(
				{"status": "success", "response": response, "reading": formatted}
			)
		else:
			return (
				jsonify(
					{
						"status": "error",
						"message": "Device not connected or command failed",
					}
				),
				404,
			)
	else:
		return jsonify({"status": "error", "message": "W610 server not running"}), 503


@app.route("/api/serial/<int:device_id>/remove", methods=["POST"])
def remove_serial_route(device_id):
	try:
		with sqlite3.connect(DB_PATH) as conn:
			cursor = conn.cursor()
			# Get device info before deleting
			cursor.execute(
				"SELECT hioki_id, ip, port FROM devices WHERE id = ? AND device_type = 'serial'",
				(device_id,),
			)
			device = cursor.fetchone()

			if not device:
				return (
					jsonify(
						{
							"status": "error",
							"message": "Serial device not found",
						}
					),
					404,
				)

			# Delete the device
			cursor.execute(
				"DELETE FROM devices WHERE id = ? AND device_type = 'serial'",
				(device_id,),
			)
			conn.commit()

			logger.info(f"Removed serial device {device[0]} at {device[1]}:{device[2]}")
			return jsonify({"status": "success"})
	except Exception as e:
		logger.error(f"Error removing serial device: {str(e)}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/w610/<int:device_id>/remove", methods=["POST"])
def remove_w610_route(device_id):
	"""Remove a W610 Hioki device by its ID."""
	try:
		with sqlite3.connect(DB_PATH) as conn:
			cursor = conn.cursor()
			cursor.execute(
				"SELECT hioki_id FROM devices WHERE id = ? AND device_type = 'w610'",
				(device_id,),
			)
			device = cursor.fetchone()

			if not device:
				return (
					jsonify({"status": "error", "message": "W610 device not found"}),
					404,
				)

			# Delete from database (no need to stop individual client)
			cursor.execute(
				"DELETE FROM devices WHERE id = ? AND device_type = 'w610'",
				(device_id,),
			)
			conn.commit()

			logger.info(f"Removed W610 device {device[0]}")
			return jsonify({"status": "success"})
	except Exception as e:
		logger.error(f"Error removing W610 device: {str(e)}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/cts/add", methods=["GET", "POST"])
def add_cts_route():
	if request.method == "POST":
		name = request.form.get("name")
		ip = request.form.get("ip")
		port = request.form.get("port", "23")  # Default port for Telnet
		cts_id = request.form.get("cts_id", "")
		cts_type = request.form.get("cts_type", "manifold")

		if name and ip:
			cts_id = add_cts(name, ip, port, cts_id, cts_type)
			logger.info(f"Added CTS device {name} at {ip}:{port}")
			return redirect(url_for("index"))

	return render_template("add_bridge.html")


@app.route("/custom/add", methods=["GET", "POST"])
def add_custom_route():
	"""Add a custom device"""
	if request.method == "POST":
		name = request.form.get("name")
		ip = request.form.get("ip")
		port = request.form.get("port")
		in_protocol = request.form.get("in_protocol")
		in_param = request.form.get("in_param")
		out_protocol = request.form.get("out_protocol")
		out_param = request.form.get("out_param")

		if name and ip and port and in_protocol and out_protocol:
			add_custom_device(
				name,
				ip,
				port,
				in_protocol,
				in_param,
				out_protocol,
				out_param,
			)
			logger.info(f"Added custom device {name} at {ip}:{port}")
			return redirect(url_for("index"))

	return render_template("add_bridge.html")


@app.route("/ultrasonic/add", methods=["GET", "POST"])
def add_ultrasonic_route():
	"""Add an Ultrasonic Welder"""
	if request.method == "POST":
		raspi_name = request.form.get("raspi_name")
		ip = request.form.get("ip")
		port = 8080
		if raspi_name and ip:
			welder_id = add_welder(raspi_name, ip, port)
			try:
				resp = requests.get(f"http://{ip}:{port}/info", timeout=2)
				if resp.ok:
					info = resp.json()
					name = info.get("name") or info.get("welder_name")
					if name:
						set_welder_name(welder_id, name)
			except Exception:
				pass
			logger.info(f"Added ultrasonic welder {raspi_name} at {ip}:{port}")
			return redirect(url_for("index"))
	return render_template("add_bridge.html")


@app.route("/api/cts/<int:cts_id>/remove", methods=["POST"])
def remove_cts_route(cts_id):
	try:
		with sqlite3.connect(DB_PATH) as conn:
			cursor = conn.cursor()
			# Get CTS info before deleting
			cursor.execute("SELECT name, ip, port FROM cts WHERE id = ?", (cts_id,))
			cts = cursor.fetchone()

			if not cts:
				return (
					jsonify({"status": "error", "message": "CTS device not found"}),
					404,
				)

			# Delete the CTS
			cursor.execute("DELETE FROM cts WHERE id = ?", (cts_id,))
			conn.commit()

			logger.info(f"Removed CTS device {cts[0]} at {cts[1]}:{cts[2]}")
			return jsonify({"status": "success"})
	except Exception as e:
		logger.error(f"Error removing CTS device: {str(e)}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/custom/<int:device_id>/remove", methods=["POST"])
def remove_custom_device(device_id):
	"""Remove a custom device by its ID"""
	try:
		with sqlite3.connect(DB_PATH) as conn:
			cursor = conn.cursor()
			cursor.execute("SELECT name FROM custom_devices WHERE id = ?", (device_id,))
			device = cursor.fetchone()

			if not device:
				return (
					jsonify(
						{
							"status": "error",
							"message": "Custom device not found",
						}
					),
					404,
				)

			cursor.execute("DELETE FROM custom_devices WHERE id = ?", (device_id,))
			conn.commit()

			logger.info(f"Removed custom device {device[0]}")
			return jsonify({"status": "success"})
	except Exception as e:
		logger.error(f"Error removing custom device: {str(e)}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/cts/<int:cts_id>/check_status", methods=["GET"])
def check_cts_status(cts_id):
	cts = get_cts_by_id(cts_id)
	if not cts:
		return (
			jsonify({"status": "error", "message": "CTS device not found"}),
			404,
		)

	try:
		# Check if CTS is reachable via telnet
		import socket

		s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
		s.settimeout(2)
		result = s.connect_ex((cts["ip"], int(cts["port"])))
		s.close()

		if result == 0:
			update_cts_status(cts_id, "online")
			return jsonify({"status": "online"})
		else:
			update_cts_status(cts_id, "offline")
			return jsonify({"status": "offline"})
	except Exception as e:
		logger.error(f"Error checking CTS status: {str(e)}")
		update_cts_status(cts_id, "offline")
		return jsonify({"status": "offline"})


@app.route("/api/serial/<int:device_id>/check_status", methods=["GET"])
def check_serial_status(device_id):
	with sqlite3.connect(DB_PATH) as conn:
		conn.row_factory = sqlite3.Row
		cursor = conn.cursor()
		cursor.execute(
			"SELECT * FROM devices WHERE id = ? AND device_type = 'serial'",
			(device_id,),
		)
		device = cursor.fetchone()

	if not device:
		return (
			jsonify({"status": "error", "message": "Serial device not found"}),
			404,
		)

	try:
		manager = COMManager()
		online = manager.check_status(device["ip"], int(device["port"]))

		if online:
			with sqlite3.connect(DB_PATH) as conn:
				cursor = conn.cursor()
				cursor.execute(
					"UPDATE devices SET status = 'online', last_update = CURRENT_TIMESTAMP WHERE id = ?",
					(device_id,),
				)
				conn.commit()
			return jsonify({"status": "online"})
		else:
			with sqlite3.connect(DB_PATH) as conn:
				cursor = conn.cursor()
				cursor.execute(
					"UPDATE devices SET status = 'offline', last_update = CURRENT_TIMESTAMP WHERE id = ?",
					(device_id,),
				)
				conn.commit()
			return jsonify({"status": "offline"})
	except Exception as e:
		logger.error(f"Error checking serial device status: {str(e)}")
		with sqlite3.connect(DB_PATH) as conn:
			cursor = conn.cursor()
			cursor.execute(
				"UPDATE devices SET status = 'offline', last_update = CURRENT_TIMESTAMP WHERE id = ?",
				(device_id,),
			)
			conn.commit()
		return jsonify({"status": "offline"})


@app.route("/api/w610/<int:device_id>/check_status", methods=["GET"])
def check_w610_status(device_id):
	"""Check status of a W610 Hioki device.

	If the device was marked offline but the last update was over 30 seconds
	ago, its status is reset to ``online``.
	"""
	with sqlite3.connect(DB_PATH) as conn:
		conn.row_factory = sqlite3.Row
		cursor = conn.cursor()
		cursor.execute(
			"SELECT status, last_update FROM devices WHERE id = ? AND device_type = 'w610'",
			(device_id,),
		)
		device = cursor.fetchone()

	if not device:
		return (
			jsonify({"status": "error", "message": "W610 device not found"}),
			404,
		)

	status = device["status"] or "online"
	if status == "offline" and device["last_update"]:
		last_dt = parse_timestamp(device["last_update"])
		if last_dt and (datetime.now() - last_dt).total_seconds() > 30:
			status = "online"
			with sqlite3.connect(DB_PATH) as conn:
				cursor = conn.cursor()
				cursor.execute(
					"UPDATE devices SET status = 'online' WHERE id = ?",
					(device_id,),
				)
				conn.commit()

	display_status = "offline" if status == "offline" else "online"

	return jsonify({"status": display_status})


@app.route("/api/device/<int:device_id>/history", methods=["GET"])
def get_device_history_route(device_id):
	"""Return recent history records for a device regardless of type."""
	try:
		limit = int(request.args.get("limit", 100))
	except ValueError:
		return jsonify({"error": "The 'limit' parameter must be a valid integer."}), 400
	history = db_service.get_device_history(device_id, limit)
	return jsonify(history)


@app.route("/api/ultrasonic/<int:welder_id>/check_status", methods=["GET"])
def check_welder_status(welder_id):
	welder = get_welder_by_id(welder_id)
	if not welder:
		return jsonify({"status": "error", "message": "Welder not found"}), 404

	url = f"http://{welder['ip']}:{welder['port']}/status"
	try:
		resp = requests.get(url, timeout=2)
		if resp.ok:
			data = resp.json()
			update_welder_status(welder_id, "online")
			update_welder_data(welder_id, data)
			return jsonify({"status": "online", "data": data})
	except Exception as e:
		logger.error(f"Error checking welder status: {e}")

	update_welder_status(welder_id, "offline")
	return jsonify({"status": "offline"})


@app.route("/api/ultrasonic/<int:welder_id>/raw_data", methods=["GET"])
def get_welder_raw_data(welder_id):
	"""Get raw data for a specific ultrasonic welder."""
	try:
		welder = get_welder_by_id(welder_id)
		if not welder:
			return (
				jsonify({"status": "error", "message": "Welder not found"}),
				404,
			)

		raw_data = {}
		if welder.get("last_reading"):
			try:
				raw_data = json.loads(welder["last_reading"])
			except json.JSONDecodeError:
				raw_data = {"raw_text": welder["last_reading"]}

		return jsonify(
			{
				"status": "success",
				"welder_name": welder.get("welder_name") or welder.get("raspi_name"),
				"raw_data": raw_data,
				"last_update": welder.get("last_update"),
				"ip": welder.get("ip"),
				"port": welder.get("port"),
			}
		)

	except Exception as e:
		logger.error(f"Error getting raw welder data: {str(e)}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/ultrasonic/<int:welder_id>/retry_mqtt", methods=["POST"])
def retry_welder_mqtt(welder_id):
	welder = get_welder_by_id(welder_id)
	if not welder:
		return jsonify({"status": "error", "message": "Welder not found"}), 404

	try:
		requests.post(f"http://{welder['ip']}:{welder['port']}/retry_mqtt", timeout=2)
		return jsonify({"status": "success"})
	except Exception as e:
		logger.error(f"Retry MQTT failed: {e}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/ultrasonic/<int:welder_id>/set_name", methods=["POST"])
def set_welder_topic(welder_id):
	welder = get_welder_by_id(welder_id)
	if not welder:
		return jsonify({"status": "error", "message": "Welder not found"}), 404

	data = request.get_json(silent=True) or {}
	name = data.get("name")
	if not name:
		return jsonify({"status": "error", "message": "Name required"}), 400

	try:
		resp = requests.post(
			f"http://{welder['ip']}:{welder['port']}/set_name",
			json={"name": name},
			timeout=2,
		)
		if resp.ok:
			set_welder_name(welder_id, name)
			return jsonify({"status": "success"})
	except Exception as e:
		logger.error(f"Set welder name failed: {e}")
		return jsonify({"status": "error", "message": str(e)}), 500

	return jsonify({"status": "error"}), resp.status_code


@app.route("/api/ultrasonic/<int:welder_id>/remove", methods=["POST"])
def remove_welder_route(welder_id):
	try:
		welder = get_welder_by_id(welder_id)
		if not welder:
			return (
				jsonify({"status": "error", "message": "Welder not found"}),
				404,
			)

		remove_welder(welder_id)
		logger.info(
			f"Removed ultrasonic welder {welder['raspi_name']} at {welder['ip']}:{welder['port']}"
		)
		return jsonify({"status": "success"})
	except Exception as e:
		logger.error(f"Error removing welder: {e}")
		return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/last_reading/<entity_type>/<identifier>", methods=["GET"])
def get_last_reading_route(entity_type, identifier):
	"""Return the most recent stored reading for a device.

	This endpoint allows external systems to retrieve the last known data for a
	device so it can be re-sent to MQTT if needed.
	"""

	data = get_last_reading(entity_type, identifier)
	if not data:
		return (
			jsonify({"status": "error", "message": "Reading not found"}),
			404,
		)
	return jsonify({"status": "success", "data": data})


def create_app():
	return app
