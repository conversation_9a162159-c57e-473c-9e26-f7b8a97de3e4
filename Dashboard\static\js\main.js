// Connect to Socket.IO server
const socket = io();

// Handle bridge status updates
socket.on('bridge_status_update', function(data) {
    const bridgeCard = document.querySelector(`.bridge-card[data-bridge-id="${data.bridge_id}"]`);
    if (bridgeCard) {
        const statusBadge = bridgeCard.querySelector('.status-badge');
        statusBadge.textContent = data.status;
        
        if (data.status === 'online') {
            statusBadge.classList.remove('bg-danger', 'bg-secondary');
            statusBadge.classList.add('bg-success');
        } else {
            statusBadge.classList.remove('bg-success', 'bg-secondary');
            statusBadge.classList.add('bg-danger');
        }
    }
});

// Handle device updates
socket.on('device_update', function(data) {
    console.log('Received device update:', data);
    
    const deviceRows = document.querySelectorAll(`tr[data-device-id="${data.device_id}"]`);
    console.log(`Found ${deviceRows.length} rows for device ID ${data.device_id}`);
    
    deviceRows.forEach(row => {
        console.log('Updating row:', row);
        // Update status badge
        const statusBadge = row.querySelector('.badge');
        if (statusBadge && data.status) {
            statusBadge.textContent = data.status;
            
            if (data.status === 'active' || data.status === 'online') {
                statusBadge.classList.remove('bg-secondary', 'bg-danger');
                statusBadge.classList.add('bg-success');
            } else {
                statusBadge.classList.remove('bg-success', 'bg-danger');
                statusBadge.classList.add('bg-secondary');
            }
        }
        
        // Update reading
        const readingCell = row.querySelector('.device-reading');
        if (readingCell && data.reading) {
            readingCell.textContent = data.reading;
            console.log(`Updated reading to ${data.reading}`);
        }
        
        // Update timestamp
        const timestampCell = row.querySelector('.device-timestamp');
        if (timestampCell && data.timestamp) {
            timestampCell.textContent = data.timestamp;
            console.log(`Updated timestamp to ${data.timestamp}`);
        }
    });
});

// Handle device reading updates for both detail and dashboard views
socket.on('device_reading_update', function(data) {
    console.log('Received device reading update:', data);
    
    // Update all device rows with matching hioki_id - try multiple selector patterns
    const deviceRows = document.querySelectorAll(`tr[data-hioki-id="${data.hioki_id}"], tr[data-device-hioki-id="${data.hioki_id}"]`);
    console.log(`Found ${deviceRows.length} rows for hioki_id ${data.hioki_id}`);
    
    deviceRows.forEach(row => {
        // Update reading - try multiple possible selectors
        const readingCells = [
            row.querySelector('.device-reading'),
            row.querySelector('td.device-reading'),
            row.querySelector('td:nth-child(4)'),
            row.querySelector('[data-reading-cell]')
        ];
        
        const readingCell = readingCells.find(cell => cell !== null);
        if (readingCell) {
            readingCell.textContent = data.reading || 'N/A';
            console.log(`Updated reading to ${data.reading}`);
        }
        
        // Update timestamp - try multiple possible selectors
        const timestampCells = [
            row.querySelector('.device-timestamp'),
            row.querySelector('.last-update-time'),
            row.querySelector('td:nth-child(5)'),
            row.querySelector('[data-timestamp-cell]')
        ];
        
        const timestampCell = timestampCells.find(cell => cell !== null);
        if (timestampCell) {
            timestampCell.textContent = new Date().toLocaleString();
        }
        
        // Update status badge
        const statusBadge = row.querySelector('.badge');
        if (statusBadge) {
            statusBadge.textContent = 'active';
            statusBadge.classList.remove('bg-secondary', 'bg-danger');
            statusBadge.classList.add('bg-success');
        }
    });
    
    // Log the update for debugging
    console.log(`Updated device ${data.hioki_id} with reading: ${data.reading}`);
});

// Handle device testing status updates
socket.on('device_testing_update', function(data) {
    const deviceRow = document.querySelector(`tr[data-hioki-id="${data.hioki_id}"]`);
    if (deviceRow) {
        if (data.testing) {
            deviceRow.classList.add('table-warning');
        } else {
            deviceRow.classList.remove('table-warning');
        }
        
        const testButton = deviceRow.querySelector('.test-reading-btn');
        if (testButton) {
            testButton.innerHTML = data.testing ? 
                '<i class="bi bi-lightning"></i> Stop Test' : 
                '<i class="bi bi-lightning"></i> Test Reading';
            testButton.setAttribute('data-testing', data.testing ? 'true' : 'false');
        }
    }
});

// Handle CTS updates
socket.on('cts_update', function(data) {
    const card = document.querySelector(`.card[data-cts-id="${data.cts_id}"]`);
    if (!card) return;
    const readingElem = card.querySelector('.cts-reading');
    if (readingElem) {
        readingElem.textContent = data.raw;
    }
    const badge = card.querySelector('.status-badge');
    if (badge) {
        badge.textContent = 'online';
        badge.classList.remove('bg-danger');
        badge.classList.add('bg-success');
    }
});

// Add a periodic refresh function for the dashboard
function refreshDashboardData() {
    // Only run this on the main dashboard page
    if (window.location.pathname === '/' || window.location.pathname === '/index') {
        fetch('/api/devices')
            .then(response => response.json())
            .then(devices => {
                devices.forEach(device => {
                    const rows = document.querySelectorAll(`tr[data-hioki-id="${device.hioki_id}"]`);
                    rows.forEach(row => {
                        // Update status
                        const statusBadge = row.querySelector('td:nth-child(3) .badge');
                        if (statusBadge) {
                            statusBadge.textContent = device.status;
                            if (device.status === 'active') {
                                statusBadge.classList.remove('bg-secondary', 'bg-danger');
                                statusBadge.classList.add('bg-success');
                            } else {
                                statusBadge.classList.remove('bg-success');
                                statusBadge.classList.add('bg-secondary');
                            }
                        }
                        
                        // Update reading
                        const readingCell = row.querySelector('.device-reading');
                        if (readingCell) {
                            readingCell.textContent = device.last_reading || 'N/A';
                        }
                        
                        // Update timestamp
                        const timestampCell = row.querySelector('.last-update-time');
                        if (timestampCell && device.last_update) {
                            try {
                                const date = new Date(device.last_update);
                                if (!isNaN(date)) {
                                    timestampCell.textContent = date.toLocaleString();
                                }
                            } catch (e) {
                                console.error('Error formatting date:', e);
                            }
                        }
                        
                        // Update testing status
                        if (device.testing === 1) {
                            row.classList.add('table-warning');
                        } else {
                            row.classList.remove('table-warning');
                        }
                    });
                });
            })
            .catch(error => {
                console.error('Error refreshing dashboard data:', error);
            });
    }
}

// Add a more aggressive periodic refresh function for the dashboard
function refreshDeviceReadings() {
    // Only run this on pages with device tables
    if (document.querySelector('table.device-table') || document.querySelector('table.devices-table')) {
        fetch('/api/devices')
            .then(response => response.json())
            .then(devices => {
                devices.forEach(device => {
                    // Try multiple selector patterns to find the right rows
                    const rows = document.querySelectorAll(
                        `tr[data-hioki-id="${device.hioki_id}"], 
                         tr[data-device-hioki-id="${device.hioki_id}"]`
                    );
                    
                    rows.forEach(row => {
                        // Update reading - try multiple possible selectors
                        const readingCells = [
                            row.querySelector('.device-reading'),
                            row.querySelector('td.device-reading'),
                            row.querySelector('td:nth-child(4)'),
                            row.querySelector('[data-reading-cell]')
                        ];
                        
                        const readingCell = readingCells.find(cell => cell !== null);
                        if (readingCell && device.last_reading) {
                            readingCell.textContent = device.last_reading || 'N/A';
                        }
                        
                        // Update timestamp - try multiple possible selectors
                        const timestampCells = [
                            row.querySelector('.device-timestamp'),
                            row.querySelector('.last-update-time'),
                            row.querySelector('td:nth-child(5)'),
                            row.querySelector('[data-timestamp-cell]')
                        ];
                        
                        const timestampCell = timestampCells.find(cell => cell !== null);
                        if (timestampCell && device.last_update) {
                            try {
                                const date = new Date(device.last_update);
                                if (!isNaN(date)) {
                                    timestampCell.textContent = date.toLocaleString();
                                }
                            } catch (e) {
                                console.error('Error formatting date:', e);
                            }
                        }
                        
                        // Update status
                        const statusBadge = row.querySelector('.badge');
                        if (statusBadge) {
                            statusBadge.textContent = device.status;
                            if (device.status === 'active') {
                                statusBadge.classList.remove('bg-secondary', 'bg-danger');
                                statusBadge.classList.add('bg-success');
                            } else {
                                statusBadge.classList.remove('bg-success');
                                statusBadge.classList.add('bg-secondary');
                            }
                        }
                    });
                });
            })
            .catch(error => {
                console.error('Error refreshing device readings:', error);
            });
    }
}

// Format timestamps when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Format timestamps
    const timestampElements = document.querySelectorAll('.last-seen-time, .last-update-time');
    timestampElements.forEach(element => {
        const timestamp = element.textContent;
        if (timestamp && timestamp !== 'N/A' && timestamp !== 'Never') {
            try {
                const date = new Date(timestamp);
                if (!isNaN(date)) {
                    element.textContent = date.toLocaleString();
                }
            } catch (e) {
                console.error('Error formatting date:', e);
            }
        }
    });
    
    // Set up periodic refresh for dashboard data
    setInterval(refreshDashboardData, 10000); // Refresh every 10 seconds
    
    // Add more frequent refresh for device readings
    setInterval(refreshDeviceReadings, 3000); // Refresh every 3 seconds
});






