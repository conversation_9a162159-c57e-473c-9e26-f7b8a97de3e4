<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}HexMES Wireless Admin{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='favicon.png') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">

    {% block extra_css %}{% endblock %}
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('index') }}">
                <img src="{{ url_for('static', filename='images/Proterra_Logo.png') }}" alt="Proterra Logo"
                    class="proterra-logo me-2">
                <img src="{{ url_for('static', filename='images/HexMES_dark.png') }}" alt="HexMES Wireless Admin"
                    class="hexmes-logo">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('list_bridges') }}">Devices</a>
                    </li>
                </ul>
                <div class="ms-auto">
                    {% if session.username %}
                    <span class="navbar-text me-2">{{ session.username }}</span>
                    <a class="btn btn-outline-light btn-sm" href="{{ url_for('logout') }}">Logout</a>
                    {% else %}
                    <a class="btn btn-outline-light btn-sm" href="{{ url_for('login') }}">Login</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        {% block content %}{% endblock %}
    </div>

    <footer class="footer text-light py-3 mt-auto">
        <div class="container d-flex justify-content-between align-items-center">
            <span>HexMES Wireless Admin &copy; 2025 Proterra</span>
            <p class="version">Version: {{ version }}</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/realtime-updates.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>

</html>