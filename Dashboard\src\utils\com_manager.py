import socket
import logging

logger = logging.getLogger(__name__)


class COMManager:
    """Utility class for simple TCP connectivity checks used by the dashboard."""

    def __init__(self, timeout=2):
        self.timeout = timeout

    def check_status(self, ip: str, port: int) -> bool:
        """Return ``True`` if the TCP port is reachable."""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(self.timeout)
            result = s.connect_ex((ip, int(port)))
            s.close()
            return result == 0
        except Exception as e:
            logger.error(f"COM check error for {ip}:{port} - {e}")
            return False

